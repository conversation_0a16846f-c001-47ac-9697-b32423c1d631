from django.shortcuts import redirect, render
from django.contrib import messages
from .tools import *
from django.http import HttpResponse,HttpResponseRedirect,JsonResponse
from django.core.mail import EmailMessage,send_mail
import os, sys, re
import time
import numpy as np
from .forms import *
import mpld3
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
import matplotlib.pyplot as plt
import pandas as pd
import json
from itertools import zip_longest
from matplotlib.gridspec import GridSpec
# Create your views here.
def home(request):
    requester_names_list,requester_gid_list = get_requester_list()
    requester_CHOICES = [('','')]
    try:
        for i in range(len(requester_names_list)):
            requester_CHOICES.append((requester_gid_list[i],requester_names_list[i]))
    except:
        pass

    if request.method == 'POST':
        
        form = Talos_access(request.POST,requester_CHOICES=requester_CHOICES)
        
        if form.is_valid():   
            
            requester = form.cleaned_data['requester']
            Password = form.cleaned_data['password']

            if requester != '' :
                
                directory = os.path.join(os.path.dirname(__file__), 'temp')
                path = os.path.join(directory, requester+'.json')
                requester_names_list,requester_gid_list,capacity_data_frame,OP_data_frame,ENG_data_frame,MaskList,ProjectMaskList, LotToConfirmList,LotToSplitList,PROJECTS,LotToManageList,AsanaProject_data_frame,LotNumber_data_frame,Email_frame,project_data_frame,LotToConfirmList_gid,LotToManageList_gid = get_data()
                asana_data =  zip_longest(requester_names_list, requester_gid_list, MaskList,ProjectMaskList,LotToConfirmList,LotToSplitList,PROJECTS,LotToManageList,LotToConfirmList_gid,LotToManageList_gid, fillvalue=None)
                df_asana = pd.DataFrame(asana_data,columns=['requester_names_list', 'requester_gid_list', 'MaskList','ProjectMaskList','LotToConfirmList','LotToSplitList','PROJECTS','LotToManageList','LotToConfirmList_gid','LotToManageList_gid'])
                dataframes_dict = {'df_asana': df_asana.to_dict(orient='records'), 'capacity_data_frame': capacity_data_frame.to_dict(orient='records'), 'OP_data_frame': OP_data_frame.to_dict(orient='records'), 'ENG_data_frame': ENG_data_frame.to_dict(orient='records'),'AsanaProject_data_frame': AsanaProject_data_frame.to_dict(orient='records'),'LotNumber_data_frame': LotNumber_data_frame.to_dict(orient='records'),'Email_frame': Email_frame.to_dict(orient='records'),'project_data_frame': project_data_frame.to_dict(orient='records')}
                with open(path, "w") as fichier_json:
                    json.dump(dataframes_dict, fichier_json)
                if Password == 'LigentecFR':
                    return redirect('/scheduling/'+requester+'_1/')
                else:
                    return redirect('/scheduling/'+requester+'_0/')
                    
                            
        else:
            error_message = form.errors          

    else:
        error_message=''
        form = Talos_access(requester_CHOICES=requester_CHOICES)
    
    return render(request, 'scheduling/home.html',locals())

def reload(request,requester_gid):
                        
    directory = os.path.join(os.path.dirname(__file__), 'temp')
    path = os.path.join(directory, requester_gid.split('_')[0]+'.json')
    requester_names_list,requester_gid_list,capacity_data_frame,OP_data_frame,ENG_data_frame,MaskList,ProjectMaskList, LotToConfirmList,LotToSplitList,PROJECTS,LotToManageList,AsanaProject_data_frame,LotNumber_data_frame,Email_frame,project_data_frame,LotToConfirmList_gid,LotToManageList_gid = get_data()
    asana_data =  zip_longest(requester_names_list, requester_gid_list, MaskList,ProjectMaskList,LotToConfirmList,LotToSplitList,PROJECTS,LotToManageList,LotToConfirmList_gid,LotToManageList_gid, fillvalue=None)
    df_asana = pd.DataFrame(asana_data,columns=['requester_names_list', 'requester_gid_list', 'MaskList','ProjectMaskList','LotToConfirmList','LotToSplitList','PROJECTS','LotToManageList','LotToConfirmList_gid','LotToManageList_gid'])
    dataframes_dict = {'df_asana': df_asana.to_dict(orient='records'), 'capacity_data_frame': capacity_data_frame.to_dict(orient='records'), 'OP_data_frame': OP_data_frame.to_dict(orient='records'), 'ENG_data_frame': ENG_data_frame.to_dict(orient='records'),'AsanaProject_data_frame': AsanaProject_data_frame.to_dict(orient='records'),'LotNumber_data_frame': LotNumber_data_frame.to_dict(orient='records'),'Email_frame': Email_frame.to_dict(orient='records'),'project_data_frame': project_data_frame.to_dict(orient='records')}
    with open(path, "w") as fichier_json:
        json.dump(dataframes_dict, fichier_json)

               
    return redirect('/scheduling/'+requester_gid+'/')
        
def split(request,requester_gid,requester_gid2,LotToSplit):
    t=requester_gid
    requester = get_requester_name(requester_gid2)
    LotToSplitList,LotWaferNumber,LayerList,CorridorList,MODULES,PROJECTS = get_Father_Lots()
    corridor = CorridorList[LotToSplitList.index(LotToSplit)]
    moduleToSplit = MODULES[LotToSplitList.index(LotToSplit)]  
    
    message1 = "Requester : "+requester     
    message2 = "Lot to split : "+LotToSplit +' modules : '+ moduleToSplit
    layer_CHOICES = [('','')]
    try:
        for e in LayerList[LotToSplitList.index(LotToSplit)]:
            layer_CHOICES.append((e,e))   
    except:
        pass


    if LotToSplit[0] == '5':
        technology = 'AN800'
    elif LotToSplit[0] == '6':
        technology = 'AN350'
    elif LotToSplit[0] == '4':
        technology = 'AN200'
   
    Platform_List_tech,modules_List,split_module_CHOICES = getmodules('operational',technology)

    if request.method == 'POST':
        
        form = Lot_split_details(request.POST,split_module_CHOICES=split_module_CHOICES,layer_CHOICES=layer_CHOICES)
        
        if form.is_valid():   
            
            Layer = form.cleaned_data['layer']
            WaferNumber = form.cleaned_data['WaferNumber']
            TestingTask = form.cleaned_data['Testing']
            ShipmentTask = form.cleaned_data['Shipment']    
            CSTask = form.cleaned_data['CS'] 
            SelectedModules=[]
            for i in range(len(Platform_List_tech)):
                SelectedModules.append(form.cleaned_data[Platform_List_tech[i]+str(i)])
            
            error_message = ''
            NewModule,error_message2 = VerifyModuleSelection(technology,SelectedModules,Platform_List_tech,modules_List)
            
            try:
                FatherLot_WaferNumber = int(float(LotWaferNumber[LotToSplitList.index(LotToSplit)]))
            except:
                FatherLot_WaferNumber = 0

            if WaferNumber == 0 :
                error_message="Please put a number of wafer superior to 0 !"

            elif (Layer == '' and NewModule == []) :
                error_message="Please choose a layer or a new modules to split !"
                error_message += error_message2
                                
            elif ShipmentTask == False :
                error_message="Shipping task is mandatory !"
            
            elif WaferNumber > FatherLot_WaferNumber :
                error_message="The wafer number must not exceed "+str(FatherLot_WaferNumber)+" wafers of the father lot!"
                     
            if error_message == '':
                    
                try:
                    error_message = create_child_lot(requester_gid2,LotToSplit,Layer,WaferNumber,TestingTask,CSTask,ShipmentTask,corridor,NewModule)
                except Exception as e:
                    error_message = f"An error has occured : {str(e)}"
                            
        else:
            error_message = form.errors          

    else:
        error_message=''
        form = Lot_split_details(split_module_CHOICES=split_module_CHOICES,layer_CHOICES=layer_CHOICES)
        
    modules_fields =  form.Get_modules(technology)    
    return render(request, 'scheduling/split.html',locals())

def capacity(request,requester_gid):
    
    requester_names_list,requester_gid_list = get_requester_list()
    if requester_gid.split('_')[0] not in requester_gid_list:
        return redirect('/scheduling/')
        
     
    TotalAccess = requester_gid.split('_')[1]
    directory = os.path.join(os.path.dirname(__file__), 'temp')
    path = os.path.join(directory, requester_gid.split('_')[0]+'.json')
    with open(path, "r") as fichier_json:
        dataframes_dict = json.load(fichier_json)
    df_asana = pd.DataFrame(dataframes_dict['df_asana'])
    
    requester_names_list = [e for e in df_asana['requester_names_list'].tolist() if e != None]
    requester_gid_list = [e for e in df_asana['requester_gid_list'].tolist() if e != None]
    requester_names_list = [e for e in df_asana['requester_names_list'].tolist() if e != None]
    MaskList = [e for e in df_asana['MaskList'].tolist() if e != None]
    ProjectMaskList = [e for e in df_asana['ProjectMaskList'].tolist() if e != None]
    LotToConfirmList = [e for e in df_asana['LotToConfirmList'].tolist() if e != None]
    LotToSplitList = [e for e in df_asana['LotToSplitList'].tolist() if e != None]
    PROJECTS = [e for e in df_asana['PROJECTS'].tolist() if e != None]
    LotToManageList = [e for e in df_asana['LotToManageList'].tolist() if e != None]
    capacity_data_frame = pd.DataFrame(dataframes_dict['capacity_data_frame']) 
    OP_data_frame = pd.DataFrame(dataframes_dict['OP_data_frame'])
    ENG_data_frame = pd.DataFrame(dataframes_dict['ENG_data_frame'])    
    
    
    AsanaProject_data_frame = pd.DataFrame(dataframes_dict['AsanaProject_data_frame']) 
    LotNumber_data_frame = pd.DataFrame(dataframes_dict['LotNumber_data_frame'])
    Email_frame = pd.DataFrame(dataframes_dict['Email_frame'])  
    project_data_frame = pd.DataFrame(dataframes_dict['project_data_frame'])  
    
    requester_CHOICES = [('','')]
    try:
        for i in range(len(requester_names_list)):
            requester_CHOICES.append((requester_gid_list[i],requester_names_list[i]))
    except:
        pass


    project_CHOICES = [('','')]
    try:
        projectList = [str(e) for e in list(capacity_data_frame['Projects']) if str(e) != 'nan']
        for e in projectList:
            project_CHOICES.append((e,e))
            
    except:
        pass


    tech_CHOICES = [('','')]
    try:
        techList = [str(e) for e in list(capacity_data_frame['Technology']) if str(e) != 'nan']
        for e in techList:
            tech_CHOICES.append((e,e))
            
    except:
        pass

    op_modules_CHOICES = []
    try:
        Platform_List_op = [str(e) for e in list(OP_data_frame['Platform']) if str(e) != 'nan']
        op_modules_List = [str(e) for e in list(OP_data_frame['Module name']) if str(e) != 'nan']
        op_modules_List_display = [str(e) for e in list(OP_data_frame['Names to be used in Talos selection']) if str(e) != 'nan']

        for e,f in zip(Platform_List_op,op_modules_List_display):
            op_modules_CHOICES.append((e,f))
            
    except:
        pass

    ENG_modules_CHOICES = []
    try:
        Platform_List_eng = [str(e) for e in list(ENG_data_frame['Platform']) if str(e) != 'nan']
        ENG_modules_List = [str(e) for e in list(ENG_data_frame['Module name']) if str(e) != 'nan']
        ENG_modules_List_display = [str(e) for e in list(ENG_data_frame['Names to be used in Talos selection']) if str(e) != 'nan']

        for e,f in zip(Platform_List_eng,ENG_modules_List_display):
            ENG_modules_CHOICES.append((e,f))       
    except:
        pass


    Mask_CHOICES = [('','')]
    try:
        for i in range(len(MaskList)):
            Mask_CHOICES.append((MaskList[i]+','+ProjectMaskList[i],MaskList[i]+','+ProjectMaskList[i]))
    except:
        pass


    reserved_lots_CHOICES = [('','')]
    try:
        for e in LotToConfirmList:
            reserved_lots_CHOICES.append((e.split(',')[0],e)) 
    except:
        pass


    confirmed_lots_CHOICES = [('','')]
    try:
        for e in range(len(LotToSplitList)):
            confirmed_lots_CHOICES.append((LotToSplitList[e],LotToSplitList[e]+','+PROJECTS[e])) 
    except:
        pass


    ALL_lots_CHOICES = [('','')]
    try:
        for e in LotToManageList:
            ALL_lots_CHOICES.append((e.split(',')[0],e)) 
    except:
        pass

    onglet=1
    ViewDisplay_1="display:inline"
    ViewDisplay_2="display:none"
    ViewDisplay_3="display:none"
    ViewDisplay_4="display:none"
    ViewDisplay_5="display:none"
    ViewDisplay_6="display:none"
    
    if request.method == 'POST':
        
        if 'op_capacity' in request.POST:
            onglet=1   
            form1 = Operational_capacity(request.POST,requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:inline"
            ViewDisplay_2="display:none"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"
            
            if form1.is_valid():   
                 
                requester = form1.cleaned_data['requester']
                Title = form1.cleaned_data['Lot_title']
                project = form1.cleaned_data['project']
                priority = form1.cleaned_data['priority']
                technology = form1.cleaned_data['technology']
                lotType = form1.cleaned_data['corridor']
                WaferNumber = form1.cleaned_data['WaferNumber']
                ChipNumber = form1.cleaned_data['ChipNumber']
                newTO = form1.cleaned_data['newTO']
                MaskRerun = form1.cleaned_data['MaskRerun']
                TODate = form1.cleaned_data['TO_date']
                SameDate = form1.cleaned_data['startSameDate']
                
                sales_order = form1.cleaned_data['sales_order']
                customer_id = form1.cleaned_data['customer_id']
                mask_design_name = form1.cleaned_data['mask_design_name']
                Fab_in_date = form1.cleaned_data['Fab_in_date']
                
                SelectedModules=[]
                for i in range(len(Platform_List_op)):
                    SelectedModules.append(form1.cleaned_data[Platform_List_op[i]+str(i)])
                
                modules,error_message = VerifyModuleSelection(technology,SelectedModules,Platform_List_op,op_modules_List)


                if requester == '':
                    error_message += '**Account or project manager is missing'
                if sales_order == '':
                    error_message += '**sales order number is missing'
                if customer_id == '':
                    error_message += '**customer id is missing'
                if Title == '':
                    error_message += '**Lot name is missing'
                if project == '':
                    error_message += '**Project is missing'
                if priority == '':
                    error_message += '**priority is missing'
                if technology == '':
                    error_message += '**technology is missing'
                if lotType == '':
                    error_message += '**corridor is missing'
                if WaferNumber == None:
                    error_message += '**wafer number is missing'
                if newTO == True and TODate == None:
                    error_message += '**Design in date is missing'      
                if newTO == True and mask_design_name == '':
                    error_message += '**Mask design name is missing'  
                if newTO == False and Fab_in_date == None:
                    error_message += '**FAB in date is missing'      
                if newTO == False and MaskRerun == '':
                    error_message += "**Please choose the Mask design name to rerun !"                     
                if project == 'MPW' and ChipNumber == None  :
                    error_message += "**Please fill the number of chips to be sent to the customer !"
                
                if newTO == True:
                    lot_initial_date = TODate
                else:
                    lot_initial_date = Fab_in_date
                    
                if error_message == '':
                    try:
                       
                        if lotType == 'PhotonixFAB':
                            LotNumber,LOT = getLotList(technology,'Engineering',str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'PhotonixFAB',str(SameDate),'0')
                        else:
                            LotNumber,LOT = getLotList(technology,lotType,str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'Prototype',str(SameDate),'0')
                            
                        RFQPOin=[str(e) for e in RFQPOin]
                        RFQPOout=[str(e) for e in RFQPOout]
                        
                        ERFPOin=[str(e) for e in ERFPOin]
                        ERFPOout=[str(e) for e in ERFPOout]
                        
                        TOin=[str(e) for e in TOin]
                        TOout=[str(e) for e in TOout]
                        PIin=[str(e) for e in PIin]
                        PIout=[str(e) for e in PIout]
                        QAout=[str(e) for e in QAout]
                        Testout=[str(e) for e in Testout]
                        DataProcessOut=[str(e) for e in DataProcessOut]
                        ShipmentOut=[str(e) for e in ShipmentOut]
                        CSout=[str(e) for e in CSout]
                        BackendOut=[str(e) for e in BackendOut]
                        check_mode = "op"
                        error_message = ""
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""
            else:
                error_message = form1.errors
                check_mode = ""

        elif "clear_fields_op" in request.POST:
            
            onglet=1   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)
            ViewDisplay_1="display:inline"
            ViewDisplay_2="display:none"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"
 
        elif 'op_confirm' in request.POST or 'op_reserve' in request.POST:
                
            onglet=1   
            form1 = Operational_capacity(request.POST,requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:inline"
            ViewDisplay_2="display:none"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"
            
            if form1.is_valid():   
                 
                requester = form1.cleaned_data['requester']
                Title = form1.cleaned_data['Lot_title']
                project = form1.cleaned_data['project']
                priority = form1.cleaned_data['priority']
                technology = form1.cleaned_data['technology']
                lotType = form1.cleaned_data['corridor']
                WaferNumber = form1.cleaned_data['WaferNumber']
                ChipNumber = form1.cleaned_data['ChipNumber']
                newTO = form1.cleaned_data['newTO']
                MaskRerun = form1.cleaned_data['MaskRerun']
                TODate = form1.cleaned_data['TO_date']
                SameDate = form1.cleaned_data['startSameDate']
                sales_order = form1.cleaned_data['sales_order']
                customer_id = form1.cleaned_data['customer_id']
                mask_design_name = form1.cleaned_data['mask_design_name']
                Fab_in_date = form1.cleaned_data['Fab_in_date']
                SelectedModules=[]
                for i in range(len(Platform_List_op)):
                    SelectedModules.append(form1.cleaned_data[Platform_List_op[i]+str(i)])
                
                modules,error_message = VerifyModuleSelection(technology,SelectedModules,Platform_List_op,op_modules_List)
                
                if newTO == True:
                    lot_initial_date = TODate
                else:
                    lot_initial_date = Fab_in_date
                
                
                if error_message == '':
                    try:
                        
                        if lotType == 'PhotonixFAB':
                            LotNumber,LOT = getLotList(technology,'Engineering',str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'PhotonixFAB',str(SameDate),'0')
                            if 'op_confirm' in request.POST:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,0,'PhotonixFAB',0)
                            else:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,1,'PhotonixFAB',0)


                        else:
                            LotNumber,LOT = getLotList(technology,lotType,str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'Prototype',str(SameDate),'0')
                            if 'op_confirm' in request.POST:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,0,'Prototype',0)
                            else:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,1,'Prototype',0)
                


                        check_mode = ""
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""

                else:
                    check_mode = ""
            else:
                error_message = form1.errors
                check_mode = ""
                 
        elif 'eng_capacity' in request.POST:
            
            
            onglet=2   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(request.POST,requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:none"
            ViewDisplay_2="display:inline"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"
            if form2.is_valid():                     
                requester = form2.cleaned_data['requester']
                Title = form2.cleaned_data['Lot_title']
                project = form2.cleaned_data['project']
                priority = form2.cleaned_data['priority']
                technology = form2.cleaned_data['technology']
                lotType = form2.cleaned_data['corridor']
                WaferNumber = form2.cleaned_data['WaferNumber']
                ChipNumber = form2.cleaned_data['ChipNumber']
                newTO = form2.cleaned_data['newTO']
                MaskRerun = form2.cleaned_data['MaskRerun']
                TODate = form2.cleaned_data['TO_date']
                SameDate = form2.cleaned_data['startSameDate']
                EngDays = form2.cleaned_data['ENG_days']
                sales_order = form2.cleaned_data['sales_order']
                customer_id = form2.cleaned_data['customer_id']
                mask_design_name = form2.cleaned_data['mask_design_name']
                Fab_in_date = form2.cleaned_data['Fab_in_date']            
    
                SelectedModules=[]
                for i in range(len(Platform_List_eng)):
                    SelectedModules.append(form2.cleaned_data[Platform_List_eng[i]+str(i)])
                
                modules,error_message = VerifyModuleSelection(technology,SelectedModules,Platform_List_eng,ENG_modules_List)

                
                if requester == '':
                    error_message += '**Account or project manager is missing'
                if sales_order == '':
                    error_message += '**sales order number is missing'
                if customer_id == '':
                    error_message += '**customer id is missing'
                if Title == '':
                    error_message += '**Lot name is missing'
                if project == '':
                    error_message += '**Project is missing'
                if priority == '':
                    error_message += '**priority is missing'
                if technology == '':
                    error_message += '**technology is missing'
                if lotType == '':
                    error_message += '**corridor is missing'
                if WaferNumber == None:
                    error_message += '**wafer number is missing'
                if newTO == True and TODate == None:
                    error_message += '**Design in date is missing'      
                if newTO == True and mask_design_name == '':
                    error_message += '**Mask design name is missing'  
                if newTO == False and Fab_in_date == None:
                    error_message += '**FAB in date is missing'      
                if newTO == False and MaskRerun == '':
                    error_message += "**Please choose the Mask design name to rerun !"                     
                if project == 'MPW' and ChipNumber == None  :
                    error_message += "**Please fill the number of chips to be sent to the customer !"
            
                if EngDays == None:
                    error_message += '**Engineering days are missing'
                elif EngDays < 5 : 
                    error_message += '**Minimum Engineering days is 5'

                if newTO == True:
                    lot_initial_date = TODate
                else:
                    lot_initial_date = Fab_in_date
                                                                            
                if error_message == '':
                    try:
                        if lotType == 'PhotonixFAB':
                            LotNumber,LOT = getLotList(technology,'Engineering',str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'PhotonixFAB',str(SameDate),str(EngDays))
                        else: 
                            LotNumber,LOT = getLotList(technology,lotType,str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'Engineering',str(SameDate),str(EngDays))
                        RFQPOin=[str(e) for e in RFQPOin]
                        RFQPOout=[str(e) for e in RFQPOout]
                        ERFPOin=[str(e) for e in ERFPOin]
                        ERFPOout=[str(e) for e in ERFPOout] 
                        TOin=[str(e) for e in TOin]
                        TOout=[str(e) for e in TOout]
                        PIin=[str(e) for e in PIin]
                        PIout=[str(e) for e in PIout]
                        QAout=[str(e) for e in QAout]
                        Testout=[str(e) for e in Testout]
                        DataProcessOut=[str(e) for e in DataProcessOut]
                        ShipmentOut=[str(e) for e in ShipmentOut]
                        CSout=[str(e) for e in CSout]
                        BackendOut=[str(e) for e in BackendOut]
                        check_mode = "eng"
                        error_message = ""
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""
            else:
                error_message = form2.errors
                check_mode = ""
 
        elif "clear_fields_eng" in request.POST:
            
            onglet=2   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:none"
            ViewDisplay_2="display:inline"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"

        elif 'eng_confirm' in request.POST or 'eng_reserve' in request.POST:
            
            
            onglet=2   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(request.POST,requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:none"
            ViewDisplay_2="display:inline"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"
            
            if form2.is_valid():                     
                requester = form2.cleaned_data['requester']
                Title = form2.cleaned_data['Lot_title']
                project = form2.cleaned_data['project']
                priority = form2.cleaned_data['priority']
                technology = form2.cleaned_data['technology']
                lotType = form2.cleaned_data['corridor']
                WaferNumber = form2.cleaned_data['WaferNumber']
                ChipNumber = form2.cleaned_data['ChipNumber']
                newTO = form2.cleaned_data['newTO']
                MaskRerun = form2.cleaned_data['MaskRerun']
                TODate = form2.cleaned_data['TO_date']
                SameDate = form2.cleaned_data['startSameDate']
                EngDays = form2.cleaned_data['ENG_days']
                sales_order = form2.cleaned_data['sales_order']
                customer_id = form2.cleaned_data['customer_id']
                mask_design_name = form2.cleaned_data['mask_design_name']
                Fab_in_date = form2.cleaned_data['Fab_in_date']   
                SelectedModules=[]
                for i in range(len(Platform_List_eng)):
                    SelectedModules.append(form2.cleaned_data[Platform_List_eng[i]+str(i)])
                
                modules,error_message = VerifyModuleSelection(technology,SelectedModules,Platform_List_eng,ENG_modules_List)
                if newTO == True:
                    lot_initial_date = TODate
                else:
                    lot_initial_date = Fab_in_date
                
                if error_message == '':

                    try:
                                            
                        
                        if lotType == 'PhotonixFAB':
                            LotNumber,LOT = getLotList(technology,'Engineering',str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'PhotonixFAB',str(SameDate),str(EngDays))
                            if 'eng_confirm' in request.POST:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,0,'PhotonixFAB',EngDays)
                            else:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,1,'PhotonixFAB',EngDays)

                            
                        else:
                            LotNumber,LOT = getLotList(technology,lotType,str(WaferNumber),str(newTO),MaskRerun,modules)
                            ERFPOin,ERFPOout,RFQPOin,RFQPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime = check_capacity_op(project,lotType,modules,LotNumber,LOT,str(lot_initial_date),str(newTO),technology,'Engineering',str(SameDate),str(EngDays))
                            if 'eng_confirm' in request.POST:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,0,'Engineering',EngDays)
                            else:
                                error_message = create_lot_project(requester,sales_order,customer_id,mask_design_name,Title,project,priority,technology,lotType,modules,str(newTO),MaskRerun,LotNumber,ChipNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,Testout,DataProcessOut,QAout,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,1,'Engineering',EngDays)

                        check_mode = ""
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""

                else:
                    check_mode = ""
            else:
                error_message = form2.errors
                check_mode = ""               
                
        elif 'lot_confirm' in request.POST or 'lot_delete' in request.POST :
            
            onglet=3   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(request.POST,requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:none"
            ViewDisplay_2="display:none"
            ViewDisplay_3="display:inline"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"
            if form3.is_valid():                     
                requester = form3.cleaned_data['requester']
                LotToConfirm = form3.cleaned_data['reserved_lots']
                try:
                    if 'lot_confirm' in request.POST:
                        error_message = confirmLot(requester,LotToConfirm)   
                    else:
                        error_message = deleteLot(LotToConfirm)   
                                  
                    check_mode = ""
                except Exception as e:
                    error_message = f"An error has occured : {str(e)}"
                    check_mode = ""
            else:
                error_message = form3.errors
                check_mode = ""                  

        elif 'lot_split' in request.POST:
            
            onglet=4   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(request.POST,requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:none"
            ViewDisplay_2="display:none"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:inline"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:none"
            
            if form4.is_valid():                     
                requester = form4.cleaned_data['requester']
                LotToSplit = form4.cleaned_data['confirmed_lots']
                try:
                    return redirect('/scheduling/split/'+requester_gid+'/'+requester+'/'+LotToSplit+'/')                
                except Exception as e:
                    error_message = f"An error has occured : {str(e)}"
                    check_mode = ""
            else:
                error_message = form4.errors
                check_mode = ""    
                
        elif 'lot_details' in request.POST or 'corridor_overview' in request.POST or 'timeline_overview' in request.POST:
            onglet=5   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview(request.POST)
            form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:none"
            ViewDisplay_2="display:none"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:inline"
            ViewDisplay_6="display:none"
            
            if form5.is_valid():  
                                   
                lotType = form5.cleaned_data['corridor']
                Date1 = form5.cleaned_data['Date1']
                Date2 = form5.cleaned_data['Date2']
                technology = form5.cleaned_data['technology']
                team = form5.cleaned_data['team']
                
                if 'lot_details' in request.POST:
                    try:          
                        lot_details = getLotListPerDate(technology,lotType,Date1,Date2,project_data_frame)    
                        check_mode = "view"
                        error_message = ""
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""
                        
                elif 'timeline_overview' in request.POST:
                    if Date1 >= Date2:
                        error_message = "Please fill correctly date 1 and date 2!" 
                    else:
                        
                        try:    
                            projects = getTimeline(team,lotType,technology,Date1,Date2,project_data_frame)
                            fig=plt.figure(figsize=(15, 10))
                            gs = GridSpec(2, 1, height_ratios=[1, 2]) 
                            ax1 = fig.add_subplot(gs[1])
                            bar_heights = np.arange(len(projects))
                            colors = plt.cm.tab10(np.linspace(0, 1, len(projects)))
                            start_dates=[]
                            due_dates=[]
                            for i, project in enumerate(projects):

                                start_date = project['start_date']
                                start_dates.append(start_date)
                                end_date = project['end_date']
                                due_dates.append(end_date)
                                duration = (end_date - start_date).days + 1
                                dates = [start_date + datetime.timedelta(days=i) for i in range(duration)]
                                lots=[0.05 for ii in range(duration)]
                                bars =ax1.bar(dates, lots,bottom=bar_heights[i]+1,width=1,color=colors[i])
                                ax1.text(bars[0].get_x(), bar_heights[i]+1.08,project['name'], ha='left', va='bottom', fontsize=12)
                           

                            ax1.vlines(x=Date1, ymin=0, ymax=len(projects)+2, color='green', linestyle='--', linewidth=2)
                            ax1.vlines(x=Date2, ymin=0, ymax=len(projects)+2, color='green', linestyle='--', linewidth=2)

                            ax1.xaxis.set_major_locator(mdates.DayLocator(interval=7))
                            ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%b-%Y"))
                            
                            ax1.set_yticks(np.arange(len(projects) + 1))
                            ax1.set_ylim(0, len(projects)+1)
                            ax1.set_xlabel('Date')
                            ax1.set_ylabel('Projects')
                            
                            fig.autofmt_xdate()      


                            LotTypeList = [str(e) for e in list(capacity_data_frame['Lot Type']) if str(e) != 'nan']
                            CapacityList=[str(e) for e in list(capacity_data_frame['Number of Lots']) if str(e) != 'nan']
                            capacity = 0
                            for i in range(len(LotTypeList)):
                                if LotTypeList[i] == lotType :
                                    capacity = int(float(CapacityList[i]))
                                    
                            if lotType == '':
                                for i in range(len(LotTypeList)):
                                    if LotTypeList[i] == 'Prototype' or LotTypeList[i] == 'Engineering':
                                        capacity += int(float(CapacityList[i]))
                                
                            FabDateList=[str(e) for e in list(capacity_data_frame['Production date in']) if str(e) != 'nan']
                            FabDateCode = [GetDayCode(e) for e in FabDateList]           


                            combined_dates = sorted([(date, 'start') for date in start_dates] + [(date, 'due') for date in due_dates])
                            dates=[]
                            lots=[]
                            nlot=0
                            for date, date_type in combined_dates:
                                dates.append(date)
                                if date_type == 'start':
                                    nlot += 1
                                else:
                                    nlot -= 1
                                lots.append(nlot)
                                

                            
                            nombre_lot_max = capacity
                            ax2 = fig.add_subplot(gs[0])
                            ax2.set_title('Projects Number per date')
                            ax2.set_xlabel('Date')
                            ax2.set_ylabel('Number of projects')
                            DATES=[]
                            LOTS=[]
                            for ii in range(len(dates)):
                                sd = dates[ii]
                                try:
                                    en = dates[ii+1]
                                    duration = (en - sd).days + 1
                                except:
                                    duration = 5
                                dates_l = [sd + datetime.timedelta(days=i) for i in range(duration)]
                                DATES=DATES+dates_l
                                LOTS=LOTS+[lots[ii] for d in dates_l]
                                
                            ax2.plot(DATES,LOTS, color='blue',linestyle='-',linewidth=2)
                            if lotType != 'PhotonixFAB':
                                try:
                                    ax2.hlines(nombre_lot_max, xmin=min(dates), xmax=max(dates), color='red', linestyle='--', label=lotType + ' capacity', linewidth=4)  
                                except:
                                    pass 
                                
                            df = pd.DataFrame({'DATES': DATES, 'LOTS': LOTS})
                            filtered_df = df[(df['DATES'] >= Date1) & (df['DATES'] <= Date2)]
                            max_lots = filtered_df['LOTS'].max()
                            min_lots = filtered_df['LOTS'].min()
                            if lotType != 'PhotonixFAB':
                                min_val = min(min_lots,nombre_lot_max)-1
                                max_val = max(max_lots,nombre_lot_max)+1
                            else:
                                min_val = min_lots-1
                                max_val = max_lots+1
                            
                            ax2.xaxis.set_major_formatter(mdates.DateFormatter("%d-%b-%Y"))
                            ax2.xaxis.set_major_locator(mdates.DayLocator(interval=7))
                            ax2.set_xlim([Date1, Date2])
                            ax2.set_ylim([min_val, max_val])
                            ax2.grid(which='both', axis='both', color='gray', linestyle=':', linewidth=0.5) 
                            fig.autofmt_xdate()
                                          
                            if lotType == '' and technology == '' :
                                title_fig = 'All projects timeline'
                            elif lotType != '' and technology == '': 
                                title_fig = 'Projects timeline on '+lotType
                            elif lotType == '' and technology != '': 
                                title_fig = technology+' projects timeline'
                            else: 
                                title_fig = technology+' projects timeline on '+lotType
                                
                            ax1.set_title(title_fig)
                            ax1.grid(which='both', axis='both', color='gray', linestyle=':', linewidth=0.5) 
                            fig.autofmt_xdate() 

                            
                            html_fig = mpld3.fig_to_html(fig,template_type='general',no_extras=True)  
                            check_mode = "view2"
                            error_message = ''
                            
                        except Exception as e:
                            error_message = f"An error has occured : {str(e)}"
                            check_mode = ""                    
                    
                    
                
                else:
                    if Date1 >= Date2:
                        error_message = "Please fill correctly date 1 and date 2!" 
                    else:
                        try:
                            Automation_config_file = OpenConfigFile()
                            capacity_data_frame = pd.read_excel(Automation_config_file,sheet_name="XFAB capacity")
                            OP_data_frame = pd.read_excel(Automation_config_file,sheet_name="Operational Lots")
                            AsanaProject_data_frame = pd.read_excel(Automation_config_file,sheet_name="Asana projects gid")

                            AsanaProjectList = [str(e) for e in list(AsanaProject_data_frame['Asana project']) if str(e) != 'nan']
                            AsanaProjectGIDList = [str(int(float(e))) for e in list(AsanaProject_data_frame['project gid']) if str(e) != 'nan']
                            pi_gid = AsanaProjectGIDList[AsanaProjectList.index('PI')]


                            LotTypeList = [str(e) for e in list(capacity_data_frame['Lot Type']) if str(e) != 'nan']
                            CapacityList=[str(e) for e in list(capacity_data_frame['Number of Lots']) if str(e) != 'nan']
                            capacity = 0
                            for i in range(len(LotTypeList)):
                                if LotTypeList[i] == lotType :
                                    capacity = int(float(CapacityList[i]))
                                    
                            if lotType == '':
                                for i in range(len(LotTypeList)):
                                    if LotTypeList[i] == 'Prototype' or LotTypeList[i] == 'Engineering':
                                        capacity += int(float(CapacityList[i]))
                                
                            FabDateList=[str(e) for e in list(capacity_data_frame['Production date in']) if str(e) != 'nan']
                            FabDateCode = [GetDayCode(e) for e in FabDateList]           


                            Pindates,dates = get_Pin_days(FabDateCode,Date1,Date2)
                            lots = getAllLotNumber(pi_gid,lotType,'',Pindates) 
                            if lotType != 'PhotonixFAB':
                                Projectlots = getAllLotNumber(pi_gid,lotType,'EIGER',Pindates)
                            nombre_lot_max = capacity
                            dates = [datetime.datetime.strptime(date, "%d-%b-%Y") for date in dates]    
                            
                            fig=plt.figure(figsize=(15, 7))
                            plt.bar(dates, lots, color='blue', label='Lot number')
                            if lotType != 'PhotonixFAB':
                                plt.bar(dates, Projectlots, color='yellow', label='EIGER')
                                plt.hlines(nombre_lot_max, xmin=min(dates), xmax=max(dates), color='red', linestyle='--', label=lotType + ' capacity', linewidth=4)
                            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter("%d-%b-%Y"))
                            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=10))
                            plt.gcf().autofmt_xdate()
                            plt.tick_params(axis='x', rotation=45)
                            plt.xlabel('Date')
                            plt.ylabel('Number of Lots')
                            plt.title(lotType)
                            plt.legend()
                            plt.grid(which='both', axis='both', color='gray', linestyle=':', linewidth=0.5) 
                            html_fig = mpld3.fig_to_html(fig,template_type='general',no_extras=True)
                            check_mode = "view2"
                            error_message = ''
                        except Exception as e:
                            error_message = f"An error has occured : {str(e)}"
                            check_mode = ""

                                          
                    
            else:
                error_message = form5.errors
                check_mode = ""

        elif 'lot_mgt_enable' in request.POST or 'lot_mgt_disable' in request.POST or 'lot_mgt_delete' in request.POST or 'lot_mgt_view' in request.POST or 'lot_mgt_modify' in request.POST or 'lot_mgt_update' in request.POST or 'lot_mgt_slot' in request.POST :
        
            onglet=6   
            form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
            form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
            form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
            form5 = Lot_overview()
            form6 = Lot_managment(request.POST,requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)

            ViewDisplay_1="display:none"
            ViewDisplay_2="display:none"
            ViewDisplay_3="display:none"
            ViewDisplay_4="display:none"
            ViewDisplay_5="display:none"
            ViewDisplay_6="display:inline"
            
            if form6.is_valid():                     
                
                LotToUpdate = form6.cleaned_data['Lots']
                requester = form6.cleaned_data['requester']
                Title = form6.cleaned_data['Lot_title']
                WaferNumber = form6.cleaned_data['WaferNumber']
                priority = form6.cleaned_data['priority']
                PONumber = form6.cleaned_data['PO']
                Sipho = form6.cleaned_data['SIFO']
                XFABdeviceID = form6.cleaned_data['XFAB_Device_ID']
                FabIN = form6.cleaned_data['FAB_date']
                DRCdate = form6.cleaned_data['DRC_date']
                corridor = form6.cleaned_data['corridor']
                project = form6.cleaned_data['project']
                add_pre_prod = form6.cleaned_data['AddPreProd']
                rm_pre_prod = form6.cleaned_data['RemovePreProd']
                sales_order = form6.cleaned_data['sales_order']
                customer_id = form6.cleaned_data['customer_id']

                
                if 'lot_mgt_delete' in request.POST:
                                       
                    try:                                 
                        check_mode = ""     
                        if LotToUpdate == '':
                            error_message = "Please choose a lot to delete !" 
                            check_mode = ""
                        else:
                            error_message = deleteLot(LotToUpdate) 
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""

                elif 'lot_mgt_enable' in request.POST:
                                       
                    try:                                 
                        check_mode = ""     
                        if LotToUpdate == '':
                            error_message = "Please choose a lot to enable !" 
                            check_mode = ""
                        else:
                            error_message = Enable_Lot(LotToUpdate) 
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""
                        
                elif 'lot_mgt_disable' in request.POST:
                                       
                    try:                                 
                        check_mode = ""     
                        if LotToUpdate == '':
                            error_message = "Please choose a lot to disable !" 
                            check_mode = ""
                        else:
                            error_message = Disable_Lot(LotToUpdate) 
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""             
                                              
                elif 'lot_mgt_modify' in request.POST:

                    try:                                 
                        check_mode = ""     
                        if LotToUpdate == '':
                            error_message = "Please choose a lot to modify !" 
                            check_mode = ""
                        elif (add_pre_prod == True and rm_pre_prod == True):
                            error_message = "Please select only to add or to remove the pre-production task !" 
                            check_mode = "" 
                        elif (FabIN != None and DRCdate != None):
                            error_message = "Please choose a new date fab or a new DRC date !" 
                            check_mode = ""                            
                        elif FabIN != None or DRCdate != None :
                            error_message = UpdateLot(LotToUpdate,requester,Title,WaferNumber,priority,PONumber,Sipho,XFABdeviceID,FabIN,DRCdate,corridor,project,add_pre_prod,rm_pre_prod,sales_order,customer_id)
                            check_mode = "" 
                        elif requester == ''  and  Title == '' and  WaferNumber == None and priority == '' and project == '' and PONumber == None and Sipho == None and sales_order == '' and customer_id == '' and XFABdeviceID == '' and add_pre_prod == False and rm_pre_prod == False:
                            error_message = "Please fill correctly the form to update the lot !" 
                            check_mode = ""                             
                        else:
                            error_message = UpdateLot(LotToUpdate,requester,Title,WaferNumber,priority,PONumber,Sipho,XFABdeviceID,FabIN,DRCdate,corridor,project,add_pre_prod,rm_pre_prod,sales_order,customer_id)
                            check_mode = ""                                                        

                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""                   
                    
                elif 'lot_mgt_view' in request.POST:
                    try:   
                        
                        if LotToUpdate == '':
                            error_message = "Please choose a lot to view !" 
                            check_mode = ""
                        else:
                        
                            LotNumber,LOT,RFQPOin,RFQPOout,ERFPOin,ERFPOout,TOin,TOout,PIin,PIout,PrePin,PrePout,Testout,DataProcessOut,ShipmentOut,CSout,BackendOut,Corridor,CycleTime,Requesters,TECH,MODULES,PROJECTLOT = getLotDetails(LotToUpdate,project_data_frame)                  
                            error_message = "" 
                            check_mode = "view3"

                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""                    

                elif 'lot_mgt_slot' in request.POST:

                    try:   
                        
                        if LotToUpdate == '' or corridor == '' or corridor == 'PhotonixFAB':
                            error_message = "Please choose a lot and corridor to find available slots according to the selected lot modules !" 
                            check_mode = ""
                        else:
                            next_slots = GetAvailableSlots(corridor,project_data_frame,capacity_data_frame,LotToUpdate,OP_data_frame)                  
                            error_message = "" 
                            check_mode = "view4"

                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""  

                elif 'lot_mgt_update' in request.POST:
                    try:                                 
                        error_message = UPDATE_ALL_TASKS() 
                        check_mode = ""
                    except Exception as e:
                        error_message = f"An error has occured : {str(e)}"
                        check_mode = ""
                             
            else:
                error_message = form6.errors
                check_mode = ""
                


    else:
        form1 = Operational_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,op_modules_CHOICES=op_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
        form2 = ENG_capacity(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,tech_CHOICES=tech_CHOICES,ENG_modules_CHOICES=ENG_modules_CHOICES,Mask_CHOICES=Mask_CHOICES)
        form3 = Lot_confirmation(requester_CHOICES=requester_CHOICES,reserved_lots_CHOICES=reserved_lots_CHOICES)
        form4 = Lot_split(requester_CHOICES=requester_CHOICES,confirmed_lots_CHOICES=confirmed_lots_CHOICES)
        form5 = Lot_overview()
        form6 = Lot_managment(requester_CHOICES=requester_CHOICES,project_CHOICES=project_CHOICES,ALL_lots_CHOICES=ALL_lots_CHOICES)
        error_message=''
        check_mode = ''
        ViewDisplay_1="display:inline"
        ViewDisplay_2="display:none"
        ViewDisplay_3="display:none"
        ViewDisplay_4="display:none"
        ViewDisplay_5="display:none"
        ViewDisplay_6="display:none"
    
    an800_modules_fields_op =  form1.Get_modules('AN800')
    an350_modules_fields_op =  form1.Get_modules('AN350')
    an200_modules_fields_op =  form1.Get_modules('AN200')
    an800_modules_fields_eng =  form2.Get_modules('AN800')
    an350_modules_fields_eng =  form2.Get_modules('AN350')
    an200_modules_fields_eng =  form2.Get_modules('AN200')
    return render(request, 'scheduling/capacity.html',locals())
