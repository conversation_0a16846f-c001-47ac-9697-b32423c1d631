﻿{% load static %}
{% load bootstrap3 %}       {# imports bootstrap3 #}
{% bootstrap_javascript %}  {# Embeds Bootstrap JS #}
{% block extrahead %}       {# Embeds Extra Resources #}
{{ form1.media }}            {# Form required JS and CSS #}
{{ form2.media }}            {# Form required JS and CSS #}
{{ form3.media }}            {# Form required JS and CSS #}
{{ form4.media }}            {# Form required JS and CSS #}
{{ form5.media }}            {# Form required JS and CSS #}
{{ form6.media }}            {# Form required JS and CSS #}
{% endblock %}              {# Ends Extra Resources #}
<header>
<table border=0 width=100%>
  <tr>
    <td align=left><img src="{% static "scheduling/logo.gif" %}" width="300" alt="logo"/></td>
    <td align=center width = "300"><font size = 5 face="Arial">200mm capacity request</font></td>
	<td align=center STYLE="border: none" width="60%"><button class="bouton_accueil" onclick="go_accueil('{{requester_gid}}')">          Home          </button></td>
  </tr>
  <tr>
    <td  width = "300"></td>
    <td width = "300"></td>
    <td align=center><a href="https://app.asana.com/0/1206956750267010/1206956750267010" target="_blank">Put your comments on Talos</a></td>
  </tr>
  <tr>
    <td  width = "300"></td>
    <td width = "300"></td>
    <td align=center><a href="https://docs.google.com/presentation/d/1pXt-J2VijrPvp1ydxUcvZzdIzl8JuFAeLtvp8TnUPQU/edit?usp=sharing" target="_blank">Talos training</a></td>
  </tr>
</table>
<br>
</header>
<div id="tabs" width=100%>
    <ul>
		<li id="onglet_op" onClick="selView(1, this)" style="border-top-left-radius: 6px;border-top-right-radius: 6px;">Operational capacity</li>
		<li id="onglet_eng" onClick="selView(2, this)" style="border-top-left-radius: 6px;border-top-right-radius: 6px;">Engineering capacity</li>
		<li id="onglet_confirm" onClick="selView(3, this)" style="border-top-left-radius: 6px;border-top-right-radius: 6px;">Lot confirmation</li>
		<li id="onglet_split" onClick="selView(4, this)" style="border-top-left-radius: 6px;border-top-right-radius: 6px;">Lot split</li>
		<li id="onglet_overview" onClick="selView(5, this)" style="border-top-left-radius: 6px;border-top-right-radius: 6px;">Lot update overview</li>
		<li id="onglet_mgt" onClick="selView(6, this)" style="border-top-left-radius: 6px;border-top-right-radius: 6px;">Lot management</li>
    </ul>
</div>
<div id="tabcontent">

    <div id="viewId1" class="tabpanel" style={{ViewDisplay_1}}>
		<div id="divGauche1">
		  <h3><strong>200mm operational lots : no process changes ! no extra metrology !</strong></font></h3>
		  <br>
		  <p><font color=red><strong>{{error_message}}</strong></font></p>
		  <br>
		  <form method="post" onsubmit="showOverlay()">
			{%csrf_token %}
			<table width=70% border=0> 
				<tr>
					<td align=left></td>
					<td align=left></td>
					<td align=left></td>
					<td align=left></td>
					<td align=left><a href="https://docs.google.com/document/d/1wAdRu1AsqoSIFUOW1uQqh8JOemHBCmBsoBEEUF6z0Pk/edit?tab=t.0" target="_blank">Naming instructions guidelines</td>
					<td align=left></td>
				</tr>
				<tr>
					<th align=left style="font-size: 20px">General informations</th>
					<th align=left style="font-size: 20px">Mask design</th>
					<th align=left colspan="2" style="font-size: 20px">Lot informations</th>
					<td align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="op_capacity" formmethod="post">         check capacity          </button></td>
					<td align=left></td>
				</tr>
				<tr>
					<td align=left>Account/Project manager</td>
					<td align=left>Require TO ?</td>
					<td align=left><div class="tooltip">Lot name
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">Examples
								<span class="tooltiptext">
									Format: &lt;optional: Project Name &gt;&lt;Mask Design Name &gt;&lt;lot purpose&gt;<br>
									Examples:<br>
									<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 60%; color: white; font-size: 14px;">
										<tr>
											<th>&lt;Lot Purpose&gt;Name</th>
											<th>Lot Name Example</th>
											<th>When to Use</th>
										</tr>
										<tr>
											<td>&lt;empty&gt;</td>
											<td>MPW-AN800-42</td>
											<td>Only 1 lot is intended to be run for that Mask Design</td>
										</tr>
										<tr>
											<td>Proto</td>
											<td>ZERMAT1 Proto</td>
											<td>The 1st lot to run from a new Mask Design. <br>An undetermined number of lots are expected to follow</td>
										</tr>
										<tr>
											<td>&lt;..&gt; Backup</td>
											<td>MPW-AN800-42 Backup<br>ZERMAT1 Proto Backup</td>
											<td>Additional lot running to mitigate the risk of failure for a critical lot.</td>
										</tr>
										<tr>
											<td>&lt;..&gt; Rerun</td>
											<td>MPW-AN800-42 Rerun<br>ZERMAT1 Proto Rerun</td>
											<td>An unforeseen repeat of a certain lot, serving the exact same purpose <br>(e.g., due to scrap)</td>
										</tr>
										<tr>
											<td>LOTx</td>
											<td>SCTR1 LOT1</td>
											<td>A fixed number of lots scheduled to run <br>(e.g., LOT1, LOT2, LOT3, etc.)</td>
										</tr>
										<tr>
											<td>DOEx</td>
											<td>ZERMAT1 DOE</td>
											<td>A lot that is scheduled to contain experiments.</td>
										</tr>
										<tr>
											<td>QUAL</td>
											<td>ZERMAT1 QUAL1</td>
											<td>A lot out of a set of lots intended to qualify the product, <br>represented by the &lt;Mask Design&gt;</td>
										</tr>
									</table>

								</span>
							</span>
						</small>						
					</div></td>
					<td align=left>Number of wafers</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left><input type="text" id="req-input1" onkeyup="filterLots('req-input1','req-select1')" placeholder="Enter requester ..."><br>{{form1.requester}}</td>
					<td align=left>{{form1.newTO}}</td>
					<td align=left>{{form1.Lot_title}}</td>
					<td align=left>{{form1.WaferNumber}}</td>
					<td align=left><button class="bouton_lancer_mesure" onclick="go('{{requester_gid}}')" name="clear_fields_op" formmethod="post">         clear all fields          </button></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left><div class="tooltip">Sales order
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									When multiple orders are linked to a lot, <br>
									separate them by semicolon ;<br>
									Example : LGT241022354; LGT240821292<br>
									If the sales order are not yet known, eg: MPW, use 0
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left><div class="tooltip">New Mask Design Name
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">Examples
								<span class="tooltiptext">
									Rules: only capital letters and numbers, no spaces, no "_"; in case of multiple tape-outs <br>for the same MPW project, append a letter<br> 
									Examples:<br>
									SCTR1<br>
									EWOC1<br>
									MERLIN5<br>
									MPW-AN800-42A, MPW-AN800-42B<br>
									!!!The following keywords are forbidden in the Mask Design Name: "lot", "run", "project"
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left>Technology platform</td>
					<td align=left>Number of chips (MPW)</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left>{{form1.sales_order}}</td>
					<td align=left>{{form1.mask_design_name}}</td>
					<td align=left>{{form1.technology}}</td>
					<td align=left>{{form1.ChipNumber}}</td>
					{% if check_mode == "op" %}
					<td align=left><button class="check_capacity_button2" onclick="go('{{requester_gid}}')" name="op_confirm" formmethod="post">         confirm lot          </button></td>
					{% else %}
					<td align=left></td>
					{% endif %}
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left><div class="tooltip">Customer id Reference
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									When multiple customers are linked to a lot, <br>
									separate them by semicolon ;<br>
									Example : 00000454; 00000166<br>
									If the customers are not yet known, eg: MPW, use 0
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left><div class="tooltip">Design in date
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									Please specify the design in date for new tape-out! 
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left><div class="tooltip">Fab in date
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									Please specify the fab in date for rerun lots with old mask! 
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left>Priority</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left>{{form1.customer_id}}</td>
					<td align=left>{{form1.TO_date}}</td>
					<td align=left>{{form1.Fab_in_date}}</td>
					<td align=left>{{form1.priority}}</td>
					{% if check_mode == "op" %}
					<td align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="op_reserve" formmethod="post">         reserve lot          </button></td>
					{% else %}
					<td align=left></td>
					{% endif %}
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left>Project</td>
					<td align=left>Mask Design Names</td>
					<td align=left>Corridor</td>
					<td align=left>Start lots in same date</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left>{{form1.project}}</td>
					<td align=left>{{form1.MaskRerun}}</td>
					<td align=left>{{form1.corridor}}</td>
					<td align=left>{{form1.startSameDate}}</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<th align=left colspan="2" style="font-size: 20px"><br>AN800 modules</th>
					<th align=left colspan="2" style="font-size: 20px"><br>AN350 modules</th>
					<th align=left colspan="2" style="font-size: 20px"><br>AN200 modules</th>
				</tr>
				<tr>
					<td align=left valign="top" colspan=2>
					{% for module,label in an800_modules_fields_op  %}
					<font color=red>{{module}} {{label}}</font><br>
					{% endfor %}
					</td>
					<td align=left valign="top" colspan=2>
					{% for module,label in an350_modules_fields_op  %}
					<font color=blue>{{module}} {{label}}</font><br>
					{% endfor %}
					</td>
					<td align=left valign="top" colspan=2>
					{% for module,label in an200_modules_fields_op  %}
					<font color=green>{{module}} {{label}}</font><br>
					{% endfor %}
					</td>
					<th align=left></th>
				  </tr>
			</table>
			{% if check_mode == "op" %}
			<br>
			<table rules=all border=1 style="border:1px solid #DEDEDE;">
				<tr>
					<th align=left ><FONT size="3" >LGT lot ID: </FONT></th>
					{% for e in LOT %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Wafer Number: </FONT></th>
					{% for e in LotNumber %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >TO date in: </FONT></th>
					{% for e in TOin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >TO date out: </FONT></th>
					{% for e in TOout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >RFQ-PO date in: </FONT></th>
					{% for e in RFQPOin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >RFQ-PO date out: </FONT></th>
					{% for e in RFQPOout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >ERFURT-PO date in: </FONT></th>
					{% for e in ERFPOin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >ERFURT-PO date out: </FONT></th>
					{% for e in ERFPOout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Production date in : </FONT></th>
					{% for e in PIin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Production date out: </FONT></th>
					{% for e in PIout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Test date out: </FONT></th>
					{% for e in Testout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Data processing out: </FONT></th>
					{% for e in DataProcessOut %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >QA gate out: </FONT></th>
					{% for e in QAout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Shipment date out: </FONT></th>
					{% for e in ShipmentOut %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >ERFURT date out: </FONT></th>
					{% for e in CSout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Backend date out : </FONT></th>
					{% for e in BackendOut %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Corridor : </FONT></th>
					{% for e in Corridor %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Cycle time (days) : </FONT></th>
					{% for e in CycleTime %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
			</table>
			{% endif %}
		  </form>
		  <br>

		<div class="overlay" id="overlay">
			<div class="message">Please wait...</div>
		</div>
		<script>
			function showOverlay() {
				document.getElementById('overlay').style.display = 'flex';
			}
		</script>
		  <br>
		  <br>
		</div>
	  </div>
	  <div id="viewId2" class="tabpanel" style={{ViewDisplay_2}}>
		{% if TotalAccess == "1" %}
		<div id="divGauche1">
		  <br>
		  <h3><strong>200mm Engineering lots : With process changes ! With extra metrology !</strong></font></h3>
		  <br>
		  <p><font color=red><strong>{{error_message}}</strong></font></p>
		  <br>
		  <form method="post" onsubmit="showOverlay2()">
			{%csrf_token %}
			<table width=70% border=0>
				<tr>
					<td align=left></td>
					<td align=left></td>
					<td align=left></td>
					<td align=left></td>
					<td align=left></td>
					<td align=left><a href="https://docs.google.com/document/d/1wAdRu1AsqoSIFUOW1uQqh8JOemHBCmBsoBEEUF6z0Pk/edit?tab=t.0" target="_blank">Naming instructions guidelines</td>
				</tr>
				<tr>
					<th align=left style="font-size: 20px">General informations</th>
					<th align=left style="font-size: 20px">Mask design</th>
					<th align=left colspan="2" style="font-size: 20px">Lot informations</th>
					<td align=left></td>
					<td align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="eng_capacity" formmethod="post">         check capacity          </button></td>
				</tr>
				<tr>
					<td align=left>Account/Project manager</td>
					<td align=left>Require TO ?</td>
					<td align=left><div class="tooltip">Lot name
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">Examples
								<span class="tooltiptext">
									Format: &lt;optional: Project Name &gt;&lt;Mask Design Name &gt;&lt;lot purpose&gt;<br>
									Examples:<br>
									<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 60%; color: white; font-size: 14px;">
										<tr>
											<th>&lt;Lot Purpose&gt;Name</th>
											<th>Lot Name Example</th>
											<th>When to Use</th>
										</tr>
										<tr>
											<td>&lt;empty&gt;</td>
											<td>MPW-AN800-42</td>
											<td>Only 1 lot is intended to be run for that Mask Design</td>
										</tr>
										<tr>
											<td>Proto</td>
											<td>ZERMAT1 Proto</td>
											<td>The 1st lot to run from a new Mask Design. <br>An undetermined number of lots are expected to follow</td>
										</tr>
										<tr>
											<td>&lt;..&gt; Backup</td>
											<td>MPW-AN800-42 Backup<br>ZERMAT1 Proto Backup</td>
											<td>Additional lot running to mitigate the risk of failure for a critical lot.</td>
										</tr>
										<tr>
											<td>&lt;..&gt; Rerun</td>
											<td>MPW-AN800-42 Rerun<br>ZERMAT1 Proto Rerun</td>
											<td>An unforeseen repeat of a certain lot, serving the exact same purpose <br>(e.g., due to scrap)</td>
										</tr>
										<tr>
											<td>LOTx</td>
											<td>SCTR1 LOT1</td>
											<td>A fixed number of lots scheduled to run <br>(e.g., LOT1, LOT2, LOT3, etc.)</td>
										</tr>
										<tr>
											<td>DOEx</td>
											<td>ZERMAT1 DOE</td>
											<td>A lot that is scheduled to contain experiments.</td>
										</tr>
										<tr>
											<td>QUAL</td>
											<td>ZERMAT1 QUAL1</td>
											<td>A lot out of a set of lots intended to qualify the product, <br>represented by the &lt;Mask Design&gt;</td>
										</tr>
									</table>

								</span>
							</span>
						</small>						
					</div></td>
					<td align=left>Number of wafers</td>
					<td align=left>Engineering days</td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left><input type="text" id="req-input2" onkeyup="filterLots('req-input2','req-select2')" placeholder="Enter requester ..."><br>{{form2.requester}}</td>
					<td align=left>{{form2.newTO}}</td>
					<td align=left>{{form2.Lot_title}}</td>
					<td align=left>{{form2.WaferNumber}}</td>
					<td align=left>{{form2.ENG_days}}</td>
					<th align=left><button class="bouton_lancer_mesure" onclick="go('{{requester_gid}}')" name="clear_fields_eng" formmethod="post">         clear all fields          </button></th>
				</tr> 
				<tr>
					<td align=left><div class="tooltip">Sales order
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									When multiple orders are linked to a lot, <br>
									separate them by semicolon ;<br>
									Example : LGT241022354; LGT240821292<br>
									If the sales order are not yet known, eg: MPW, use 0
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left><div class="tooltip">New Mask Design Name
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">Examples
								<span class="tooltiptext">
									Rules: only capital letters and numbers, no spaces, no "_"; in case of multiple tape-outs <br>for the same MPW project, append a letter<br> 
									Examples:<br>
									SCTR1<br>
									EWOC1<br>
									MERLIN5<br>
									MPW-AN800-42A, MPW-AN800-42B<br>
									!!!The following keywords are forbidden in the Mask Design Name: "lot", "run", "project"
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left>Technology platform</td>
					<td align=left>Number of chips (MPW)</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left>{{form2.sales_order}}</td>
					<td align=left>{{form2.mask_design_name}}</td>
					<td align=left>{{form2.technology}}</td>
					<td align=left>{{form2.ChipNumber}}</td>
					<td align=left></td>
					{% if  check_mode == "eng" %}
					<td align=left><button class="check_capacity_button2" onclick="go('{{requester_gid}}')" name="eng_confirm" formmethod="post">         confirm lot          </button></td>
					{% else %}
					<td align=left></td>
					{% endif %}  
				</tr> 
				<tr>
					<td align=left><div class="tooltip">Customer id Reference
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									When multiple customers are linked to a lot, <br>
									separate them by semicolon ;<br>
									Example : 00000454; 00000166<br>
									If the customers are not yet known, eg: MPW, use 0
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left><div class="tooltip">Design in date
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									Please specify the design in date for new tape-out! 
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left><div class="tooltip">Fab in date
						<br>
						<small style="font-size: 12px; color: grey;">
							<span class="tooltip">more info
								<span class="tooltiptext">
									Please specify the fab in date for rerun lots with old mask! 
								</span>
							</span>
						</small>						
					</div></td>
					<td align=left>Priority</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left>{{form2.customer_id}}</td>
					<td align=left>{{form2.TO_date}}</td>
					<td align=left>{{form2.Fab_in_date}}</td>
					<td align=left>{{form2.priority}}</td>
					<td align=left></td>
					{% if  check_mode == "eng" %}
					<td align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="eng_reserve" formmethod="post">         reserve lot          </button></td>
					{% else %}
					<td align=left></td>
					{% endif %}
				</tr> 
				<tr>
					<td align=left>Project</td>
					<td align=left>Mask Design Names</td>
					<td align=left>Corridor</td>
					<td align=left>Start lots in same date</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<td align=left>{{form2.project}}</td>
					<td align=left>{{form2.MaskRerun}}</td>
					<td align=left>{{form2.corridor}}</td>
					<td align=left>{{form2.startSameDate}}</td>
					<td align=left></td>
					<td align=left></td>
				</tr> 
				<tr>
					<th align=left colspan="2" style="font-size: 20px"><br>AN800 modules</th>
					<th align=left colspan="2" style="font-size: 20px"><br>AN350 modules</th>
					<th align=left colspan="2" style="font-size: 20px"><br>AN200 modules</th>
				</tr>
				<tr>
					<td align=left valign="top" colspan=2>
					{% for module,label in an800_modules_fields_eng  %}
					<font color=red>{{module}} {{label}}</font><br>
					{% endfor %}
					</td>
					<td align=left valign="top" colspan=2>
					{% for module,label in an350_modules_fields_eng  %}
					<font color=blue>{{module}} {{label}}</font><br>
					{% endfor %}
					</td>
					<td align=left valign="top" colspan=2>
					{% for module,label in an200_modules_fields_eng  %}
					<font color=green>{{module}} {{label}}</font><br>
					{% endfor %}
					</td>
					<th align=left></th>
				</tr>
			</table>
			{% if check_mode == "eng" %}
			<br>
			<table rules=all border=1 style="border:1px solid #DEDEDE;">
				<tr>
					<th align=left ><FONT size="3" >LGT lot ID: </FONT></th>
					{% for e in LOT %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Wafer Number: </FONT></th>
					{% for e in LotNumber %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >TO date in: </FONT></th>
					{% for e in TOin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >TO date out: </FONT></th>
					{% for e in TOout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >RFQ-PO date in: </FONT></th>
					{% for e in RFQPOin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >RFQ-PO date out: </FONT></th>
					{% for e in RFQPOout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >ERFURT-PO date in: </FONT></th>
					{% for e in ERFPOin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >ERFURT-PO date out: </FONT></th>
					{% for e in ERFPOout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Production date in : </FONT></th>
					{% for e in PIin %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Production date out: </FONT></th>
					{% for e in PIout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Test date out: </FONT></th>
					{% for e in Testout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Data processing out: </FONT></th>
					{% for e in DataProcessOut %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >QA gate out: </FONT></th>
					{% for e in QAout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Shipment date out: </FONT></th>
					{% for e in ShipmentOut %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >ERFURT date out: </FONT></th>
					{% for e in CSout %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Backend date out : </FONT></th>
					{% for e in BackendOut %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Corridor : </FONT></th>
					{% for e in Corridor %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
				<tr>
					<th align=left ><FONT size="3" >Cycle time (days) : </FONT></th>
					{% for e in CycleTime %}
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					{% endfor  %}
				</tr>
			</table>
			{% endif %}
		  </form>
		  <br>
		  <div class="overlay" id="overlay2">
			<div class="message">Please wait...</div>
		</div>
		{% endif %}
		<script>
			function showOverlay2() {
				document.getElementById('overlay2').style.display = 'flex';
			}
		</script>
		  <br>
		  <br>
		</div>
	  </div>
	  <div id="viewId3" class="tabpanel" style={{ViewDisplay_3}}>
		<div id="divGauche1">
		  <br>
		  <h3><strong>Confirmation of the lot reservation : </strong></font></h3>
		  <br>
		  <br>
		  <p><font color=red><strong>{{error_message}}</strong></font></p>
		  <br>
		  <br>
		  <form method="post" onsubmit="showOverlay3()">
			{%csrf_token %}
			<table width=50% border=0>
			  <tr>
				<th align=left>Account/Project manager :</th>
				<th align=left>Reserved lots :</th>
				<td align=left><button class="check_capacity_button" onclick="go3('{{requester_gid}}')" name="lot_confirm" formmethod="post">         confirm          </button></td>
			  </tr>
			  <tr>
				<td align=left><input type="text" id="req-input3" onkeyup="filterLots('req-input3','req-select3')" placeholder="Enter requester ..."><br><br>{{form3.requester}}</td>
				<td align=left>{{form3.reserved_lots}}</td>
				<td align=left><button class="check_capacity_button" onclick="go4('{{requester_gid}}')" name="lot_delete" formmethod="post">         delete          </button></td>
			  </tr>  
			</table>
		  </form>
		  <br>
		  <div class="overlay" id="overlay3">
			<div class="message">Please wait...</div>
		</div>
		<script>
			function showOverlay3() {
				document.getElementById('overlay3').style.display = 'flex';
			}
		</script>
		  <br>
		  <br>
		</div>
	  </div>
	  <div id="viewId4" class="tabpanel" style={{ViewDisplay_4}}>
		<div id="divGauche1">
		  <br>
		  <h3><strong>Wafer split/drop off : </strong></font></h3>
		  <br>
		  <br>
		  <p><font color=red><strong>{{error_message}}</strong></font></p>
		  <br>
		  <br>
		  <form method="post" onsubmit="showOverlay4()">
			{%csrf_token %}
			<table width=50% border=0>
			  <tr>
				<th align=left>Account/Project manager :</th>
				<th align=left>Confirmed lots to split:</th>
				<td align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_split" formmethod="post">         OK          </button></td>
			  </tr>
			  <tr>
				<td align=left><input type="text" id="req-input4" onkeyup="filterLots('req-input4','req-select4')" placeholder="Enter requester ..."><br><br></td>
				<td align=left><input type="text" id="lot-input2" onkeyup="filterLots('lot-input2','lot-select2')" placeholder="Enter lot number ..."></td>
				<td align=left></td>
			  </tr>  
			  <tr>
				<td align=left>{{form4.requester}}</td>
				<td align=left>{{form4.confirmed_lots}}</td>
				<td align=left></td>
			  </tr>  
			</table>
		  </form>
		  <br>
		  <div class="overlay" id="overlay4">
			<div class="message">Please wait...</div>
		</div>
		<script>
			function showOverlay4() {
				document.getElementById('overlay4').style.display = 'flex';
			}
		</script>
		  <br>
		  <br>
		</div>
	  </div>
	  <div id="viewId5" class="tabpanel" style={{ViewDisplay_5}}>
		<div id="divGauche1">
		  <br>
		  <h3><strong> Real-time corridor overview : </strong></font></h3>
		  <br>
		  <br>
		  <p><font color=red><strong>{{error_message}}</strong></font></p>
		  <br>
		  <br>
		  <form method="post" onsubmit="showOverlay5()">
			{%csrf_token %}
			<table width=50% border=0>
			  <tr>
				<th align=left>Task type :</th>
				<th align=left>Technology :</th>
				<th align=left>Corridor :</th>
				<th align=left> Date 1:</th>
				<th align=left> Date 2:</th>
				<td align=left><button class="check_capacity_button" onclick="go3('{{requester_gid}}')" name="corridor_overview" formmethod="post">         Corridor overview          </button></td>
			  </tr>
			  <tr>
				<td align=left>{{form5.team}}</td>
				<td align=left>{{form5.technology}}</td>
				<td align=left>{{form5.corridor}}</td>
				<td align=left>{{form5.Date1}}</td>
				<td align=left>{{form5.Date2}}</td>
				<td align=left><button class="check_capacity_button" onclick="go4('{{requester_gid}}')" name="lot_details" formmethod="post">         Get lot details          </button></td>
			  </tr>  
			  <tr>
				<th align=left></th>
				<th align=left></th>
				<th align=left></th>
				<th align=left></th>
				<th align=left></th>
				<td align=left><button class="check_capacity_button" onclick="go5('{{requester_gid}}')" name="timeline_overview" formmethod="post">         Display Timeline          </button></td>
			  </tr>
			</table>
			{% if check_mode == "view2" %}
			<div id="fig_container">
				{{ html_fig2|safe }}
			 </div>		
			 <div id="fig_container">
				{{ html_fig|safe }}
			 </div>		
			{% endif %}
			{% if check_mode == "view" %}
			<br>
			<br>
			<br>
			<div class="scrollable-table-container">
			<table rules=all border=1 width=100% style="border:1px solid #DEDEDE;">
				<tr>
					<th align=left ><FONT size="3" >LGT lot ID: </FONT></th>
					<th align=left ><FONT size="3" >Account/Project manager :</FONT></th>
					<th align=left ><FONT size="3" >Wafer Number: </FONT></th>
					<th align=left ><FONT size="3" >Corridor : </FONT></th>
					<th align=left ><FONT size="3" >Cycle time (days) : </FONT></th>
					<th align=left ><FONT size="3" >TO date in: </FONT></th>
					<th align=left ><FONT size="3" >TO date out: </FONT></th>
					<th align=left ><FONT size="3" >RFQ-PO date in: </FONT></th>
					<th align=left ><FONT size="3" >RFQ-PO date out: </FONT></th>
					<th align=left ><FONT size="3" >ERFURT-PO date in: </FONT></th>
					<th align=left ><FONT size="3" >ERFURT-PO date out: </FONT></th>
					<th align=left ><FONT size="3" >Pre-production date in: </FONT></th>
					<th align=left ><FONT size="3" >Pre-production date out: </FONT></th>
					<th align=left ><FONT size="3" >Production date in : </FONT></th>
					<th align=left ><FONT size="3" >Production date out : </FONT></th>
					<th align=left ><FONT size="3" >Test date out: </FONT></th>
					<th align=left ><FONT size="3" >Data processing out: </FONT></th>
					<th align=left ><FONT size="3" >Shipment date out: </FONT></th>
					<th align=left ><FONT size="3" >ERFURT date out: </FONT></th>
					<th align=left ><FONT size="3" >Backend date out : </FONT></th>
				</tr>
				{% for a,b,c,d,e,f,g,h,i,h1,i1,j,k,l,m,n,o,p,q,r in lot_details %}
				<tr>
					<td align=left><FONT size="3" >{{a}}</FONT></td>
					<td align=left><FONT size="3" >{{b}}</FONT></td>
					<td align=left><FONT size="3" >{{c}}</FONT></td>
					<td align=left><FONT size="3" >{{d}}</FONT></td>
					<td align=left><FONT size="3" >{{e}}</FONT></td>
					<td align=left><FONT size="3" >{{f}}</FONT></td>
					<td align=left><FONT size="3" >{{g}}</FONT></td>
					<td align=left><FONT size="3" >{{h}}</FONT></td>
					<td align=left><FONT size="3" >{{i}}</FONT></td>
					<td align=left><FONT size="3" >{{h1}}</FONT></td>
					<td align=left><FONT size="3" >{{i1}}</FONT></td>
					<td align=left><FONT size="3" >{{j}}</FONT></td>
					<td align=left><FONT size="3" >{{k}}</FONT></td>
					<td align=left><FONT size="3" color=red>{{l}}</FONT></td>
					<td align=left><FONT size="3" >{{m}}</FONT></td>
					<td align=left><FONT size="3" >{{n}}</FONT></td>
					<td align=left><FONT size="3" >{{o}}</FONT></td>
					<td align=left><FONT size="3" >{{p}}</FONT></td>
					<td align=left><FONT size="3" >{{q}}</FONT></td>
					<td align=left><FONT size="3" >{{r}}</FONT></td>
				</tr>
				{% endfor  %}
			</table>
			</div>
			{% endif %}
		  </form>
		  <br>
		<div class="overlay" id="overlay5">
			<div class="message">Please wait...</div>
		</div>
		<script>
			function showOverlay5() {
				document.getElementById('overlay5').style.display = 'flex';
			}
		</script>
		  <br>
		  <br>
		</div>
	  </div>
	  <div id="viewId6" class="tabpanel" style={{ViewDisplay_6}}>
		{% if TotalAccess == "1" %}
		<div id="divGauche1">
		<br>
		<h3><strong>200mm lot management & update :</strong></font></h3>
		<br>
		<br>
		<p><font color=red><strong>{{error_message}}</strong></font></p>
		<br>
		<br>
		<form method="post" onsubmit="showOverlay6()">
		  {%csrf_token %}
		  <table width=70% border=0>
			<tr>
			  <th align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_mgt_delete" formmethod="post">         delete          </button></th>
			  <th align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_mgt_modify" formmethod="post">         modify          </button></th>
			  <th align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_mgt_view" formmethod="post">         view          </button></th>
			  <th align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_mgt_slot" formmethod="post">         Get next available slots          </button></th>
			  <th align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_mgt_update" formmethod="post">         Update all lots          </button></th>
			  <th align=left></th>
			</tr>
			<tr>
				<th align=left>Lots :</th>
				<th align=left></th>
				<th align=left></th>
				<th align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_mgt_enable" formmethod="post">         Enable lot          </button></th>
				<th align=left><button class="check_capacity_button" onclick="go('{{requester_gid}}')" name="lot_mgt_disable" formmethod="post">         Disable lot          </button></th>
				<th align=left></th>
			</tr>
			<tr>
			  <td align=left><input type="text" id="lot-input" onkeyup="filterLots('lot-input','lot-select')" placeholder="Enter lot number ..."><br><br>{{form6.Lots}}</td>
			  <td align=left></td>
			  <td align=left></td>
			  <td align=left></td>
			  <td align=left></td>
			  <th align=left></th>
			</tr>  
			<tr>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			</tr>
  
			<tr>
			  <th align=left>Account/Project manager :</th>
			  <th align=left>Number of wafers :</th>
			  <th align=left>Priority :</th>
			  <th align=left>Mask SiFo ID :</th>
			  <th align=left><font color=red>New FAB in date :</font></th>
			  <th align=left><font color=red>New Design in date :</font></th>
			</tr>
			<tr>
			  <td align=left><input type="text" id="req-input6" onkeyup="filterLots('req-input6','req-select6')" placeholder="Enter requester ..."><br><br>{{form6.requester}}</td>
			  <td align=left>{{form6.WaferNumber}}</td>
			  <th align=left>{{form6.priority}}</th>
			  <th align=left>{{form6.SIFO}}</th>
			  <th align=left>{{form6.FAB_date}}</th>
			  <th align=left>{{form6.DRC_date}}</th>
			</tr> 
			<tr>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			  <th align=left><br></th>
			</tr>
			<tr>
			  <th align=left>Lot title :</th>
			  <th align=left>Project :</th>
			  <th align=left>FAB PO number :</th>
			  <th align=left>XFAB device ID :</th>
			  <th align=left>{{form6.AddPreProd}} {{form6.AddPreProd.label}}</th>
			  <th align=left><font color=red> Corridor :</font></th>
			</tr>
			<tr>
			  <td align=left>{{form6.Lot_title}}</td>
			  <td align=left>{{form6.project}}</td>
			  <th align=left>{{form6.PO}}</th>
			  <th align=left>{{form6.XFAB_Device_ID}}</th>
			  <th align=left>{{form6.RemovePreProd}} {{form6.RemovePreProd.label}}</th>
			  <th align=left>{{form6.corridor}}</th>
			</tr> 
			<tr>
				<th align=left><br></th>
				<th align=left><br></th>
				<th align=left><br></th>
				<th align=left><br></th>
				<th align=left><br></th>
				<th align=left><br></th>
			  </tr>
			  <tr>
				<th align=left>Sales order :</th>
				<th align=left>Customer id Reference :</th>
				<th align=left></th>
				<th align=left></th>
				<th align=left></th>
				<th align=left></th>
			  </tr>
			  <tr>
				<th align=left>{{form6.sales_order}}</th>
				<th align=left>{{form6.customer_id}}</th>
				<th align=left></th>
				<th align=left></th>
				<th align=left></th>
				<th align=left></th>
			  </tr>
		  </table>
		  {% if check_mode == "view3" %}
		  <br>
		  <table rules=all border=1 width=50% style="border:1px solid #DEDEDE;">
			  <tr>
				  <th align=left ><FONT size="3" >Lot title : </FONT></th>
				  {% for e in LOT %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				<th align=left ><FONT size="3" >Account/Project manager :</FONT></th>
				{% for e in Requesters %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			  </tr>
			  <tr>
				<th align=left ><FONT size="3" >Technology : </FONT></th>
				{% for e in TECH %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			  </tr>
			  <tr>
				<th align=left ><FONT size="3" >Modules : </FONT></th>
				{% for e in MODULES %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			  </tr>
			  <tr>
				<th align=left ><FONT size="3" >Corridor : </FONT></th>
				{% for e in Corridor %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			</tr>
			  <tr>
				<th align=left ><FONT size="3" >Project : </FONT></th>
				{% for e in PROJECTLOT %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Wafer Number: </FONT></th>
				  {% for e in LotNumber %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >TO date in: </FONT></th>
				  {% for e in TOin %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >TO date out: </FONT></th>
				  {% for e in TOout %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >RFQ-PO date in: </FONT></th>
				  {% for e in RFQPOin %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >RFQ-PO date out: </FONT></th>
				  {% for e in RFQPOout %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				<th align=left ><FONT size="3" >ERFURT-PO date in: </FONT></th>
				{% for e in ERFPOin %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			</tr>
			<tr>
				<th align=left ><FONT size="3" >ERFURT-PO date out: </FONT></th>
				{% for e in ERFPOout %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			</tr>
			  <tr>
				  <th align=left ><FONT size="3" >Pre-production date in: </FONT></th>
				  {% for e in PrePin %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Pre-production date out: </FONT></th>
				  {% for e in PrePout %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Production date in : </FONT></th>
				  {% for e in PIin %}
				  <td align=left><FONT size="3" color="red">{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Production date out: </FONT></th>
				  {% for e in PIout %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Test date out: </FONT></th>
				  {% for e in Testout %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Data processing out: </FONT></th>
				  {% for e in DataProcessOut %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Shipment date out: </FONT></th>
				  {% for e in ShipmentOut %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >ERFURT-FAB date out: </FONT></th>
				  {% for e in CSout %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				  <th align=left ><FONT size="3" >Backend date out : </FONT></th>
				  {% for e in BackendOut %}
				  <td align=left><FONT size="3" >{{e}}</FONT></td>
				  {% endfor  %}
			  </tr>
			  <tr>
				<th align=left ><FONT size="3" >Cycle time (days) : </FONT></th>
				{% for e in CycleTime %}
				<td align=left><FONT size="3" >{{e}}</FONT></td>
				{% endfor  %}
			  </tr>
		  </table>
		  {% elif check_mode == "view4" %}
		  <br>
		  <table rules=all border=1 width=50% style="border:1px solid #DEDEDE;">
			  <tr>
				  <th align=left ><FONT size="3" >Corridor </FONT></th>
				  <th align=left ><FONT size="3" >Available DRC clean dates </FONT></th>
				  <th align=left ><FONT size="3" >Available FAB in dates</FONT></th>
			  </tr>
			  {% for c,d,f in next_slots %}
			  <tr>
				<td align=left><FONT size="3" >{{c}}</FONT></td>
				<td align=left><FONT size="3" >{{d}}</FONT></td>
				<td align=left><FONT size="3" >{{f}}</FONT></td>
			  </tr>
			  {% endfor  %}
		  </table>
		  {% endif %}
		</form>
		<br>
		<div class="overlay" id="overlay6">
			<div class="message">Please wait...</div>
		</div>
		<script>
			function showOverlay6() {
				document.getElementById('overlay6').style.display = 'flex';
			}
		</script>
		<br>
		<br>
		</div>
		{% endif %}
	  </div>

	<footer> <font size = 2> Internal use only : contain confidential informations !</font> </footer>


</div>




<SCRIPT type="text/javascript">
  function go(gid) {
		window.location = "/scheduling/"+gid+"/"	
}
</SCRIPT>
<SCRIPT type="text/javascript">
  function go2(gid) {
	window.open("/scheduling/"+gid+"/","", "width=400,height=400");  
   }
</SCRIPT>
<SCRIPT type="text/javascript">
  function go_process(op,prod,SN) {
	window.open("/scheduling/","", "width=800,height=800"); 
   }
</SCRIPT>
<SCRIPT type="text/javascript">
  function go_save(op,prod,SN){
		var largeur=((screen.width)-400)/2;  
		var hauteur=((screen.height)-150)/2;
		window.open("/process_FAB/SWS/"+op+"/"+prod+"/"+SN+"/"+"0"+"/","","width=400,height=150,left="+largeur+",top="+hauteur+"");
   }
</SCRIPT>

<SCRIPT type="text/javascript">
	function go_accueil(gid) {
			window.location = "/scheduling/reload/"+gid+"/"
				
		}
</SCRIPT>

<script>
	function filterLots(inputId, selectId) {
		let input, filter, select, options, i;
		input = document.getElementById(inputId);
		filter = input.value.toUpperCase();
		select = document.getElementById(selectId);
		options = select.getElementsByTagName("option");
		let matchingOptions = [];
		for (i = 0; i < options.length; i++) {
			let txtValue = options[i].textContent || options[i].innerText;
			if (txtValue.toUpperCase().indexOf(filter) > -1) {
				options[i].style.display = "";
				matchingOptions.push(options[i]);
			} else {
				options[i].style.display = "none";
			}
		}

		// If there's exactly one match, select it
		if (matchingOptions.length === 1) {
			select.value = matchingOptions[0].value;
		} else {
			// If no exact match, deselect any selected option
			select.value = "";
		}
	}
</script>


<script>

function selInit() {


	if ({{onglet}} == 1){

	var ong=document.getElementById("onglet_op");
	ong.style.borderBottom = "0px solid gray";
	ong.style.background = '#FFFFFF';
	
	var ong=document.getElementById("onglet_eng");
	ong.style.borderBottom = "1px solid gray";
	ong.style.background = '#E2E2E2';

	var ong=document.getElementById("onglet_confirm");
	ong.style.borderBottom = "1px solid gray";
	ong.style.background = '#E2E2E2';
	
	var ong=document.getElementById("onglet_split");
	ong.style.borderBottom = "1px solid gray";
	ong.style.background = '#E2E2E2';

	var ong=document.getElementById("onglet_overview");
	ong.style.borderBottom = "1px solid gray";
	ong.style.background = '#E2E2E2';

	var ong=document.getElementById("onglet_mgt");
	ong.style.borderBottom = "1px solid gray";
	ong.style.background = '#E2E2E2';

	}
	if ({{onglet}} == 2){

		var ong=document.getElementById("onglet_op");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
		
		var ong=document.getElementById("onglet_eng");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
		
		var ong=document.getElementById("onglet_confirm");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_split");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_overview");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_mgt");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
	}
	if ({{onglet}} == 3){

		var ong=document.getElementById("onglet_op");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
		
		var ong=document.getElementById("onglet_eng");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_confirm");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
		
		var ong=document.getElementById("onglet_split");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_overview");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_mgt");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
	} 
	if ({{onglet}} == 4){

		var ong=document.getElementById("onglet_op");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
		
		var ong=document.getElementById("onglet_eng");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_confirm");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_split");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
	
		var ong=document.getElementById("onglet_overview");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_mgt");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
	}
	if ({{onglet}} == 5){

		var ong=document.getElementById("onglet_op");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
		
		var ong=document.getElementById("onglet_eng");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_confirm");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_split");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_overview");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
	
		var ong=document.getElementById("onglet_mgt");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
	}
	if ({{onglet}} == 6){

		var ong=document.getElementById("onglet_op");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
		
		var ong=document.getElementById("onglet_eng");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_confirm");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
		
		var ong=document.getElementById("onglet_split");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_overview");
		ong.style.borderBottom = "1px solid gray";
		ong.style.background = '#E2E2E2';
	
		var ong=document.getElementById("onglet_mgt");
		ong.style.borderBottom = "0px solid gray";
		ong.style.background = '#FFFFFF';
	
	}


}
function selView(n, litag) {

	var view1 = "none";
	var view2 = "none";
	var view3 = "none";
	var view4 = "none";
	var view5 = "none";
	var view6 = "none";
	

	switch(n) {
		case 1:
			view1 = "inline";
			break;
		case 2:
			view2 = "inline";
			break;
		case 3:
			view3 = "inline";
			break;
		case 4:
			view4 = "inline";
			break;
		case 5:
			view5 = "inline";
			break;		
    case 6:
			view6 = "inline";
		  break;

		// add how many cases you need
		default:
			alert("You must select a submodule!");
	}
	document.getElementById("viewId1").style.display = view1;
	document.getElementById("viewId2").style.display = view2;
	document.getElementById("viewId3").style.display = view3;
	document.getElementById("viewId4").style.display = view4;
	document.getElementById("viewId5").style.display = view5;
	document.getElementById("viewId6").style.display = view6;

	var tabs = document.getElementById("tabs");
	var ca = Array.prototype.slice.call(tabs.querySelectorAll("li"));
	ca.map(function(elem) {
		elem.style.background="#E2E2E2";
		elem.style.borderBottom="1px solid gray"
	});
	litag.style.borderBottom = "0px solid white";
	litag.style.background = "white";
}


window.onload=function() {
	selInit()
}
</script>



<style type="text/css">

ul{
	list-style-type:none;
	margin-left: 0;
    padding-left: 0;
	
}

.bouton_accueil{
	border:1px solid gray;
	border-radius:5px;
	background-color:#FFEBFF;
	height:30px;
	width:250px;
}

.bouton_accueil:hover{
	background-color:#FFDDFF;
}

.bouton_analyse{
	border:1px solid gray;
	border-radius:5px;
	background-color:#D6CDF5;
	height:75px;
	width:250px;
}

.bouton_analyse:hover{
	background-color:#BDAFEE;
}

.bouton_continu{
	border:1px solid gray;
	border-radius:5px;
	background-color:#DEDEDE;
	height:75px;
	width:250px;
}

.bouton_continu:hover{
	background-color:#C2C2C2;
}

.bouton_suivi{
	border:1px solid gray;
	border-radius:5px;
	height:75px;
	width:250;
	background-color:#A3EC98;
}

.bouton_process{
	border:1px solid gray;
	border-radius:5px;
	height:75px;
	width:250;
	background-color:#F6C6DD;
}

.bouton_suivi:hover{
	background-color:#87EB78;
}

.bouton_save{
	border:1px solid gray;
	border-radius:5px;
	height:75px;
	width:250;
	background-color:#B5E6E8;
}

.bouton_save:hover{
	background-color:97DFE2;
}

.check_capacity_button2{
	border:1px solid gray;
	border-radius:5px;
	background-color:#F4DED0;
	height:30px;
	width:250;
}

.check_capacity_button2:hover{
	background-color:#b6d7a8;
	cursor:pointer;
}


.check_capacity_button{
	border:1px solid gray;
	border-radius:5px;
	background-color:#F4DED0;
	height:30px;
	width:250;
}

.check_capacity_button:hover{
	background-color:F2C2A3;
	cursor:pointer;
}

.bouton_lancer_mesure{
	border:1px solid gray;
	border-radius:5px;
	background-color:#B5E6E8;
	height:30px;
	width:250;
}

.bouton_lancer_mesure:hover{
	background-color:B5E6E8;
	cursor:pointer;
}


#tabs {
    	width:100%;
    	height:auto;
		margin : 0 ;
}

#tabs ul
{
    	font: normal 14px arial, sans, sans-serif;
    	margin: 0;
    	padding:0 0 0px 0;
    	width:auto;
	border-bottom: 0px solid gray;
	padding-bottom : 0px;
}

#tabs ul li 
{
	display: inline-block;
    	float:left;
    	height:50px;
    	min-width:200px;
    	text-align:center;
    	line-height: 40px;
    	padding:0 0px 0 0px;
    	margin: 1px 0px 0px 0px;
    	border: 1px solid gray;
    	border-top-left-radius: 0px;
    	border-top-right-radius: 0px;	
    	background:#E2E2E2;
}

#tabcontent {
    position:relative;
	background:#FFF;
	width:100%;
	height:auto;	
	border:0px solid #999;
	border-top:none;
	margin-top:0;
	overflow:hidden;
}


#divGauche1{
float:left;
width:100%;
height:auto;
overflow:none;/*pour activer les scrollbarres*/
overflow:hidden;
}

</style>
<style>


	.overlay {
		position: fixed;
		top: 170;
		left: 170;
		width: 50%;
		height: 10%;
		background-color: rgba(197, 144, 232, 0.5);
		display: none;
		justify-content: center;
		align-items: center;
		border-radius: 5px;
	}

	.message {
		background-color: white;
		padding: 20px;
		border-radius: 5px;
	    color: red; 
        font-weight: bold; 
		width:400px;
	}
</style>

<style>
	.scrollable-table-container {
		width: 100%; /* Largeur maximale du conteneur */
		overflow-x: auto; /* Activer la barre de défilement horizontale si nécessaire */
	}
	th, td {
		padding: 8px; /* Ajouter de l'espace à l'intérieur des cellules */
		white-space: nowrap; /* Empêcher les retours à la ligne */
		max-width: 500px; /* Largeur maximale de la cellule */
	}
</style>
<style>
	/* Style pour le tooltip */
	.tooltip {
	  position: relative;
	  display: inline-block;
	  cursor: pointer;
	}
	
	.tooltip .tooltiptext {
	  visibility: hidden;
	  width: auto;
	  max-width: 800px; /* Largeur maximale du popup */
	  background-color: #555;
	  color: #fff;
	  text-align: left;
	  border-radius: 5px;
	  padding: 15px; /* Espacement intérieur */
	  position: absolute;
	  z-index: 1;
	  top: 100%; /* Positionner le tooltip en dessous de l'élément */
	  left: 100%; 
	  transform: translateX(-20%); /* Centrer le tooltip horizontalement */
	  opacity: 0;
	  transition: opacity 0.3s;
	  white-space: nowrap; /* Permettre les retours à la ligne */
	  line-height: 1.6; /* Espacement entre les lignes */
	  font-size: 14px; /* Taille de police */
	}
	
	.tooltip:hover .tooltiptext {
	  visibility: visible;
	  opacity: 1;
	}
</style>