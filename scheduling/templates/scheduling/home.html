﻿{% load static %}
<header>
<table border=0 width=30%>
    <tr>
    <td align=left><img src="{% static "scheduling/logo.gif" %}" width="300"/></td>
    <td align=center width = "100"><font size = 5 face="Arial">Authentification</font></td>
    </tr>
</table>
<br>
</header>
<br>
<form method="post">
{%csrf_token %}
<table width=30% border=0>
<tr>
    <th align=center>Username </th>
    <th align=center>Password [Optional]</th>

</tr>
<tr>
    <td align=center>{{form.requester}}</td>
    <td align=center>{{form.password}}</td>

</tr>  
<tr>
    <th align=left><br></th>
</tr>  
<tr>
    <td align=center><br><button class="check_capacity_button" onclick="go()" formmethod="post">         Login          </button></td>
</tr> 
</table>
</form>

<SCRIPT type="text/javascript">
    function go() {

            window.location = "/scheduling/"
                
        }
    </SCRIPT>



<style type="text/css">

ul{
    list-style-type:none;
    margin-left: 0;
    padding-left: 0;
    
}

.bouton_accueil{
    border:1px solid gray;
    border-radius:5px;
    background-color:#FFEBFF;
    height:30px;
    width:250px;
}

.bouton_accueil:hover{
    background-color:#FFDDFF;
}

.bouton_analyse{
    border:1px solid gray;
    border-radius:5px;
    background-color:#D6CDF5;
    height:75px;
    width:250px;
}

.bouton_analyse:hover{
    background-color:#BDAFEE;
}

.bouton_continu{
    border:1px solid gray;
    border-radius:5px;
    background-color:#DEDEDE;
    height:75px;
    width:250px;
}

.bouton_continu:hover{
    background-color:#C2C2C2;
}

.bouton_suivi{
    border:1px solid gray;
    border-radius:5px;
    height:75px;
    width:250;
    background-color:#A3EC98;
}

.bouton_process{
    border:1px solid gray;
    border-radius:5px;
    height:75px;
    width:250;
    background-color:#F6C6DD;
}

.bouton_suivi:hover{
    background-color:#87EB78;
}

.bouton_save{
    border:1px solid gray;
    border-radius:5px;
    height:75px;
    width:250;
    background-color:#B5E6E8;
}

.bouton_save:hover{
    background-color:97DFE2;
}

.check_capacity_button{
    border:1px solid gray;
    border-radius:5px;
    background-color:#F4DED0;
    height:30px;
    width:150;
}

.check_capacity_button:hover{
    background-color:F2C2A3;
    cursor:pointer;
}

.bouton_lancer_mesure{
    border:1px solid gray;
    border-radius:5px;
    background-color:#F4DED0;
    height:75px;
    width:250;
}

.bouton_lancer_mesure:hover{
    background-color:F2C2A3;
    cursor:pointer;
}

#tabs {
        width:100%;
        height:auto;
        margin : 0 ;
}

#tabs ul
{
        font: normal 14px arial, sans, sans-serif;
        margin: 0;
        padding:0 0 0px 0;
        width:auto;
    border-bottom: 0px solid gray;
    padding-bottom : 0px;
}

#tabs ul li 
{
    display: inline-block;
        float:left;
        height:50px;
        min-width:300px;
        text-align:center;
        line-height: 40px;
        padding:0 0px 0 0px;
        margin: 1px 0px 0px 0px;
        border: 1px solid gray;
        border-top-left-radius: 0px;
        border-top-right-radius: 0px;	
        background:#E2E2E2;
}

#tabcontent {
    position:relative;
    background:#FFF;
    width:100%;
    height:1000px;	
    border:0px solid #999;
    border-top:none;
    margin-top:0;
    overflow:hidden;
}


#divGauche1{
float:left;
width:100%;
height:auto;
overflow:none;/*pour activer les scrollbarres*/
overflow:hidden;

</style>