{% load static %}
<header>
<table border=0 width=70%>
    <tr>
    <td align=left><img src="{% static "scheduling/logo.gif" %}" width="300" alt="logo"/></td>
    <td align=center width = "300"><font size = 5 face="Arial">200mm Wafer split</font></td>
    <td align=center STYLE="border: none" width="60%"><button class="bouton_accueil" onclick="go_accueil('{{requester_gid}}')">          Back          </button></td>
    </tr>
</table>
<br>
</header>
<br> 
<h4>{{message1}}</h4>
<br>
<h4>{{message2}}</h4>
<br>
<p><font color=red><strong>{{error_message}}</strong></font></p>
<br>
<form method="post" onsubmit="showOverlay()">
{%csrf_token %}
<table width=50% border=0>
<tr>
    <th align=left>Layer code (Drop off case):</th>
    <th align=left>New modules (Split case) :</th>
    <th align=left>Number of wafers :</th>
    <th align=left>Requested tasks :</th>
</tr>
<tr>
    <td align=left valign="top">{{form.layer}}</td>
    <td align=left valign="top">
        {% for module,label in modules_fields  %}
        <font color=blue>{{module}} {{label}}</font><br>
        {% endfor %}
    </td>
    <td align=left valign="top">{{form.WaferNumber}}</td>
    <td align=left valign="top">
        {{form.CS}} <font color=red><strong>Chip singulation (Drop off case)</strong></font><br>
        {{form.Testing}} <font color=red><strong>Testing</strong></font><br>
        {{form.Shipment}} <font color=red><strong>Shipment</strong></font><br>
    </td>
</tr>  
<tr>
    <td align=left><br></td>
    <td align=left><br></td>
    <td align=left><br></td>
    <td align=left><br><button class="check_capacity_button" onclick="go('{{requester_gid}}','{{requester_gid2}}','{{LotToSplit}}')" formmethod="post">         Lot split          </button></td>
</tr> 
</table>
</form>
<div class="overlay" id="overlay">
    <div class="message">Please wait...</div>
</div>
<script>
    function showOverlay() {
        document.getElementById('overlay').style.display = 'flex';
    }
</script>
<SCRIPT type="text/javascript">
    function go(gid,gid2,lot) {

            window.location = "/scheduling/split/"+gid+"/"+gid2+"/"+lot+"/"
                
        }
    </SCRIPT>

<SCRIPT type="text/javascript">
function go_accueil(gid) {
        window.location = "/scheduling/"+gid+"/"
            
    }
</SCRIPT>
<style type="text/css">

ul{
    list-style-type:none;
    margin-left: 0;
    padding-left: 0;
    
}

.bouton_accueil{
    border:1px solid gray;
    border-radius:5px;
    background-color:#FFEBFF;
    height:30px;
    width:250px;
}

.bouton_accueil:hover{
    background-color:#FFDDFF;
}

.bouton_analyse{
    border:1px solid gray;
    border-radius:5px;
    background-color:#D6CDF5;
    height:75px;
    width:250px;
}

.bouton_analyse:hover{
    background-color:#BDAFEE;
}

.bouton_continu{
    border:1px solid gray;
    border-radius:5px;
    background-color:#DEDEDE;
    height:75px;
    width:250px;
}

.bouton_continu:hover{
    background-color:#C2C2C2;
}

.bouton_suivi{
    border:1px solid gray;
    border-radius:5px;
    height:75px;
    width:250;
    background-color:#A3EC98;
}

.bouton_process{
    border:1px solid gray;
    border-radius:5px;
    height:75px;
    width:250;
    background-color:#F6C6DD;
}

.bouton_suivi:hover{
    background-color:#87EB78;
}

.bouton_save{
    border:1px solid gray;
    border-radius:5px;
    height:75px;
    width:250;
    background-color:#B5E6E8;
}

.bouton_save:hover{
    background-color:97DFE2;
}

.check_capacity_button{
    border:1px solid gray;
    border-radius:5px;
    background-color:#F4DED0;
    height:30px;
    width:250;
}

.check_capacity_button:hover{
    background-color:F2C2A3;
    cursor:pointer;
}

.bouton_lancer_mesure{
    border:1px solid gray;
    border-radius:5px;
    background-color:#F4DED0;
    height:75px;
    width:250;
}

.bouton_lancer_mesure:hover{
    background-color:F2C2A3;
    cursor:pointer;
}

#tabs {
        width:100%;
        height:auto;
        margin : 0 ;
}

#tabs ul
{
        font: normal 14px arial, sans, sans-serif;
        margin: 0;
        padding:0 0 0px 0;
        width:auto;
    border-bottom: 0px solid gray;
    padding-bottom : 0px;
}

#tabs ul li 
{
    display: inline-block;
        float:left;
        height:50px;
        min-width:300px;
        text-align:center;
        line-height: 40px;
        padding:0 0px 0 0px;
        margin: 1px 0px 0px 0px;
        border: 1px solid gray;
        border-top-left-radius: 0px;
        border-top-right-radius: 0px;	
        background:#E2E2E2;
}

#tabcontent {
    position:relative;
    background:#FFF;
    width:100%;
    height:1000px;	
    border:0px solid #999;
    border-top:none;
    margin-top:0;
    overflow:hidden;
}


#divGauche1{
float:left;
width:100%;
height:auto;
overflow:none;/*pour activer les scrollbarres*/
overflow:hidden;

</style>
<style>
	.overlay {
		position: fixed;
		top: 170;
		left: 170;
		width: 50%;
		height: 10%;
		background-color: rgba(197, 144, 232, 0.5);
		display: none;
		justify-content: center;
		align-items: center;
		border-radius: 5px;
	}

	.message {
		background-color: white;
		padding: 20px;
		border-radius: 5px;
	    color: red; 
        font-weight: bold; 
		width:400px;
	}
</style>