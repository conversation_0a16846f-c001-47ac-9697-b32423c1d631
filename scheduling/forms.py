from django import forms
from django.forms import formset_factory, widgets
from bootstrap_datepicker_plus.widgets import DatePickerInput
import datetime
from .tools import *


class Talos_access(forms.Form) :

    def __init__(self,*args,**kwargs):
        requester_CHOICES = kwargs.pop('requester_CHOICES')
        super(Talos_access,self).__init__(*args,**kwargs)
        self.fields['requester']  = forms.ChoiceField(required=True,choices=requester_CHOICES)
        self.fields['password'] = forms.CharField(required=False,widget=forms.PasswordInput)


class Operational_capacity(forms.Form) :

    def __init__(self,*args,**kwargs):
        
        requester_CHOICES = kwargs.pop('requester_CHOICES')
        project_CHOICES = kwargs.pop('project_CHOICES')
        tech_CHOICES = kwargs.pop('tech_CHOICES')
        op_modules_CHOICES = kwargs.pop('op_modules_CHOICES')
        Mask_CHOICES = kwargs.pop('Mask_CHOICES')

        super(Operational_capacity,self).__init__(*args,**kwargs)
        self.fields['requester']  = forms.ChoiceField(required=False,choices=requester_CHOICES, widget=forms.Select(attrs={'id': 'req-select1'}))
        self.fields['sales_order'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['customer_id'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['project']  = forms.ChoiceField(required=False,choices=project_CHOICES)
        self.fields['newTO']  = forms.BooleanField(required=False,initial = True)
        self.fields['mask_design_name'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['MaskRerun']  = forms.ChoiceField(required=False,choices=Mask_CHOICES)
        self.fields['TO_date'] =  forms.DateField(required=False,widget=DatePickerInput(format='%d/%m/%Y'))
        self.fields['Lot_title'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['Fab_in_date'] =  forms.DateField(required=False,widget=DatePickerInput(format='%d/%m/%Y'))
        self.fields['technology']  = forms.ChoiceField(required=False,choices=tech_CHOICES)
        self.fields['corridor']  = forms.ChoiceField(required=False,choices=[('',''),('Prototype','Prototype'),('PhotonixFAB','PhotonixFAB')])
        self.fields['WaferNumber'] = forms.IntegerField(required=False, min_value = 1)
        self.fields['ChipNumber'] = forms.IntegerField(required=False, min_value = 1)        
        self.fields['startSameDate']  = forms.BooleanField(required=False,initial = False)
        self.fields['priority']  = forms.ChoiceField(required=False,choices=[('',''),('Low','Low'),('Medium','Medium'),('High','High')])
        
        for i in range(len(op_modules_CHOICES)):
            self.fields[op_modules_CHOICES[i][0]+str(i)] = forms.BooleanField(required=False,initial = False,label=op_modules_CHOICES[i][1])

    def Get_modules(self,technology):
        Modules = []
        Labels = []
        for name in self.fields:
            if name.startswith(technology):
                Modules.append(self[name])
                Labels.append(self[name].label)
        return zip(Modules,Labels)


class ENG_capacity(forms.Form) :

    def __init__(self,*args,**kwargs):
        
        requester_CHOICES = kwargs.pop('requester_CHOICES')
        project_CHOICES = kwargs.pop('project_CHOICES')
        tech_CHOICES = kwargs.pop('tech_CHOICES')
        ENG_modules_CHOICES = kwargs.pop('ENG_modules_CHOICES')
        Mask_CHOICES = kwargs.pop('Mask_CHOICES')

        super(ENG_capacity,self).__init__(*args,**kwargs)
        
        self.fields['requester']  = forms.ChoiceField(required=False,choices=requester_CHOICES, widget=forms.Select(attrs={'id': 'req-select2'}))
        self.fields['sales_order'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['customer_id'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['project']  = forms.ChoiceField(required=False,choices=project_CHOICES)
        self.fields['newTO']  = forms.BooleanField(required=False,initial = True)
        self.fields['mask_design_name'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['MaskRerun']  = forms.ChoiceField(required=False,choices=Mask_CHOICES)
        self.fields['TO_date'] =  forms.DateField(required=False,widget=DatePickerInput(format='%d/%m/%Y'))
        self.fields['Lot_title'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['Fab_in_date'] =  forms.DateField(required=False,widget=DatePickerInput(format='%d/%m/%Y'))
        self.fields['technology']  = forms.ChoiceField(required=False,choices=tech_CHOICES)
        self.fields['corridor']  = forms.ChoiceField(required=False,choices=[('',''),('Engineering','Engineering'),('PhotonixFAB','PhotonixFAB')])
        self.fields['WaferNumber'] = forms.IntegerField(required=False, min_value = 1)
        self.fields['ChipNumber'] = forms.IntegerField(required=False, min_value = 1)        
        self.fields['startSameDate']  = forms.BooleanField(required=False,initial = False)
        self.fields['priority']  = forms.ChoiceField(required=False,choices=[('',''),('Low','Low'),('Medium','Medium'),('High','High')])
        self.fields['ENG_days'] = forms.IntegerField(required=False, min_value = 0)
        for i in range(len(ENG_modules_CHOICES)):
            self.fields[ENG_modules_CHOICES[i][0]+str(i)] = forms.BooleanField(required=False,initial = False,label=ENG_modules_CHOICES[i][1])
                    
            
            
    def Get_modules(self,technology):
        Modules = []
        Labels = []
        for name in self.fields:
            if name.startswith(technology):
                Modules.append(self[name])
                Labels.append(self[name].label)
        return zip(Modules,Labels)



class Lot_confirmation(forms.Form) :

    def __init__(self,*args,**kwargs):
        
        requester_CHOICES = kwargs.pop('requester_CHOICES')
        reserved_lots_CHOICES = kwargs.pop('reserved_lots_CHOICES')
        super(Lot_confirmation,self).__init__(*args,**kwargs)
        
        self.fields['requester']  = forms.ChoiceField(required=True,choices=requester_CHOICES, widget=forms.Select(attrs={'id': 'req-select3'}))
        self.fields['reserved_lots'] = forms.ChoiceField(required=True,choices=reserved_lots_CHOICES)
    


class Lot_split(forms.Form) :

    def __init__(self,*args,**kwargs):
        
        requester_CHOICES = kwargs.pop('requester_CHOICES')
        confirmed_lots_CHOICES = kwargs.pop('confirmed_lots_CHOICES')
        super(Lot_split,self).__init__(*args,**kwargs)
        self.fields['requester']  = forms.ChoiceField(required=True,choices=requester_CHOICES, widget=forms.Select(attrs={'id': 'req-select4'}))
        self.fields['confirmed_lots'] = forms.ChoiceField(required=True,choices=confirmed_lots_CHOICES, widget=forms.Select(attrs={'id': 'lot-select2'}))


class Lot_split_details(forms.Form) :

    def __init__(self,*args,**kwargs):
        
        layer_CHOICES = kwargs.pop('layer_CHOICES')
        split_module_CHOICES = kwargs.pop('split_module_CHOICES')
        super(Lot_split_details,self).__init__(*args,**kwargs)
        self.fields['layer']  = forms.ChoiceField(required=False,choices=layer_CHOICES)
        self.fields['WaferNumber'] = forms.IntegerField(required=True, min_value = 1)
        self.fields['CS']  = forms.BooleanField(required=False,initial = False)
        self.fields['Testing']  = forms.BooleanField(required=False,initial = True)
        self.fields['Shipment']  = forms.BooleanField(required=False,initial = True)
        for i in range(len(split_module_CHOICES)):
            self.fields[split_module_CHOICES[i][0]+str(i)] = forms.BooleanField(required=False,initial = False,label=split_module_CHOICES[i][1])
            
    def Get_modules(self,technology):
        Modules = []
        Labels = []
        for name in self.fields:
            if name.startswith(technology):
                Modules.append(self[name])
                Labels.append(self[name].label)
        return zip(Modules,Labels)


class Lot_overview(forms.Form) :

    corridor  = forms.ChoiceField(required=False,choices=[('',''),('Prototype','Prototype'),('Engineering','Engineering'),('Pre-production','Pre-production'),('PhotonixFAB','PhotonixFAB'),('Inactive (Proto)','Inactive (Proto)'),('Inactive (Eng)','Inactive (Eng)')])
    Date1 = forms.DateField(required=True,widget=DatePickerInput(format='%d/%m/%Y'),initial=datetime.date(datetime.date.today().year,1,1))
    Date2 = forms.DateField(required=False,widget=DatePickerInput(format='%d/%m/%Y'),initial=datetime.date(datetime.date.today().year,12,31))
    technology  = forms.ChoiceField(required=False,choices=[('',''),('AN800','AN800'),('AN350','AN350'),('AN200','AN200')])
    team  = forms.ChoiceField(required=False,choices=[('',''),('PI','PI'),('Pre-production','Pre-production'),('Tape-Out','Tape-Out'),('RFQ-PO','RFQ-PO'),('ERFURT-PO','ERFURT-PO'),('Testing','Testing'),('Data processing','Data processing'),('Shipment','Shipment'),('ERFURT-FAB','ERFURT-FAB'),('Backend','Backend')])


class Lot_managment(forms.Form) :

    

    def __init__(self,*args,**kwargs):
        
        requester_CHOICES = kwargs.pop('requester_CHOICES')
        project_CHOICES = kwargs.pop('project_CHOICES')
        ALL_lots_CHOICES = kwargs.pop('ALL_lots_CHOICES')
        super(Lot_managment,self).__init__(*args,**kwargs)       
        self.fields['requester']  = forms.ChoiceField(required=False,choices=requester_CHOICES, widget=forms.Select(attrs={'id': 'req-select6'}))
        self.fields['Lot_title'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['project']  = forms.ChoiceField(required=False,choices=project_CHOICES)
        self.fields['corridor']  = forms.ChoiceField(required=False,choices=[('',''),('Prototype','Prototype'),('Engineering','Engineering'),('PhotonixFAB','PhotonixFAB')])
        self.fields['WaferNumber'] = forms.IntegerField(required=False, min_value = 1)
        self.fields['priority']  = forms.ChoiceField(required=False,choices=[('',''),('Low','Low'),('Medium','Medium'),('High','High')])
        self.fields['FAB_date'] = forms.DateField(required=False,widget=DatePickerInput(format='%d/%m/%Y'))
        self.fields['DRC_date'] = forms.DateField(required=False,widget=DatePickerInput(format='%d/%m/%Y'))
        self.fields['PO'] = forms.IntegerField(required=False, min_value = 0)
        self.fields['SIFO'] = forms.IntegerField(required=False, min_value = 0)
        self.fields['XFAB_Device_ID'] = forms.CharField(required=False, max_length=100)
        self.fields['Lots']  = forms.ChoiceField(required=False,choices=ALL_lots_CHOICES,widget=forms.Select(attrs={'id': 'lot-select'}))
        self.fields['AddPreProd'] = forms.BooleanField(required=False,initial = False,label='Add pre-production task')
        self.fields['RemovePreProd'] = forms.BooleanField(required=False,initial = False,label='Remove pre-production task')
        self.fields['sales_order'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))
        self.fields['customer_id'] = forms.CharField(required=False, max_length=100,widget = forms.TextInput(attrs={'size':'30'}))        


