{"df_asana": [{"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "464436269489794", "MaskList": "AAA", "ProjectMaskList": "05-46 EIGER 2.10", "LotToConfirmList": null, "LotToSplitList": "6AAAC205M09", "PROJECTS": "LER3", "LotToManageList": "6AAAC205M09,LER3"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "467496932920604", "MaskList": "AAB", "ProjectMaskList": "LER DEV", "LotToConfirmList": null, "LotToSplitList": "6AAAC204M09", "PROJECTS": "TW13 Qualification rerun", "LotToManageList": "6AAAC204M09,TW13 Qualification rerun"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "489733785249552", "MaskList": "AAC", "ProjectMaskList": "LER3", "LotToConfirmList": null, "LotToSplitList": "6AAAC203M09", "PROJECTS": "TW13 Qualification", "LotToManageList": "6AAAC203M09,TW13 Qualification"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "513842320205279", "MaskList": "AAD", "ProjectMaskList": "05-54 EIGER 2.11 UCUT", "LotToConfirmList": null, "LotToSplitList": "6AAAC202M09", "PROJECTS": "TW13 Qualification", "LotToManageList": "6AAAC202M09,TW13 Qualification"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "794540907829992", "MaskList": "AAE", "ProjectMaskList": "05-56 MPW31", "LotToConfirmList": null, "LotToSplitList": "6AABN202M09", "PROJECTS": "E2.20", "LotToManageList": "6AABN202M09,E2.20"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "869201729673462", "MaskList": "AAF", "ProjectMaskList": "Rerun 05-43 MPW29a", "LotToConfirmList": null, "LotToSplitList": "6AABN201M09", "PROJECTS": "E2.20", "LotToManageList": "6AABN201M09,E2.20"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "669147563988443", "MaskList": "AAG", "ProjectMaskList": "05-55 UCSD", "LotToConfirmList": null, "LotToSplitList": "6AABM201M09", "PROJECTS": "E2.12 redesign", "LotToManageList": "6AABM201M09,E2.12 redesign"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1131443696074648", "MaskList": "AAH", "ProjectMaskList": "05-61_LIGH", "LotToConfirmList": null, "LotToSplitList": "6AABL201M09", "PROJECTS": "E2.12", "LotToManageList": "6AABL201M09,E2.12"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1125291682985446", "MaskList": "AAI", "ProjectMaskList": "05-60_PAKH", "LotToConfirmList": null, "LotToSplitList": "6AABK202M14", "PROJECTS": "E2.23b", "LotToManageList": "6AABK202M14,E2.23b"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1160066995528264", "MaskList": "AAL", "ProjectMaskList": "MPW32AB", "LotToConfirmList": null, "LotToSplitList": "6AABK201M14", "PROJECTS": "E2.23a", "LotToManageList": "6AABK201M14,E2.23a"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1171139149669535", "MaskList": "AAM", "ProjectMaskList": "E2.14 TEL", "LotToConfirmList": null, "LotToSplitList": "6AABJ202M14", "PROJECTS": "E7.2", "LotToManageList": "6AABJ202M14,E7.2"}, {"requester_names_list": "Ana Catarina <PERSON>", "requester_gid_list": "1183107046965382", "MaskList": "AAN", "ProjectMaskList": "EIGER 2.16", "LotToConfirmList": null, "LotToSplitList": "6AABJ201M14", "PROJECTS": "E7.2", "LotToManageList": "6AABJ201M14,E7.2"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1191400555194352", "MaskList": "AAO", "ProjectMaskList": "<PERSON><PERSON><PERSON>", "LotToConfirmList": null, "LotToSplitList": "6AABI203M09", "PROJECTS": "E6.6", "LotToManageList": "6AABI203M09,E6.6"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1193626179934205", "MaskList": "AAR", "ProjectMaskList": "ZTEP", "LotToConfirmList": null, "LotToSplitList": "6AABI202M09", "PROJECTS": "E6.6", "LotToManageList": "6AABI202M09,E6.6"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199110939357003", "MaskList": "AAS", "ProjectMaskList": "Dynamos 2", "LotToConfirmList": null, "LotToSplitList": "6AABI201M09", "PROJECTS": "E6.6", "LotToManageList": "6AABI201M09,E6.6"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199394325037760", "MaskList": "AAT", "ProjectMaskList": "MPW33AB", "LotToConfirmList": null, "LotToSplitList": "6AABH204M09", "PROJECTS": "E2.18", "LotToManageList": "6AABH204M09,E2.18"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1199990271835482", "MaskList": "AAW", "ProjectMaskList": "Glider 3.1", "LotToConfirmList": null, "LotToSplitList": "6AABH203M09", "PROJECTS": "E2.17", "LotToManageList": "6AABH203M09,E2.17"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1200295708067936", "MaskList": "AAX", "ProjectMaskList": "LGT labs PD MTP", "LotToConfirmList": null, "LotToSplitList": "6AABH202M09", "PROJECTS": "E2.15a", "LotToManageList": "6AABH202M09,E2.15a"}, {"requester_names_list": "<PERSON> Yao", "requester_gid_list": "1200692689203662", "MaskList": "AAY", "ProjectMaskList": "EIGR8p4 second lot ", "LotToConfirmList": null, "LotToSplitList": "6AABH201M09", "PROJECTS": "E2.15 TEL", "LotToManageList": "6AABH201M09,E2.15 TEL"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201328860961631", "MaskList": "ABB", "ProjectMaskList": "redesign EIGR6p4", "LotToConfirmList": null, "LotToSplitList": "6AAAB202M09", "PROJECTS": "LER DEV", "LotToManageList": "6AAAB202M09,LER DEV"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201466301932731", "MaskList": "ABC", "ProjectMaskList": "MAGNIFY WP2", "LotToConfirmList": null, "LotToSplitList": "6AABG104M08", "PROJECTS": "E2.19c", "LotToManageList": "6AABG104M08,E2.19c"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201752762206320", "MaskList": "ABD", "ProjectMaskList": "Quix 1", "LotToConfirmList": null, "LotToSplitList": "6AABG103M08", "PROJECTS": "E2.19b", "LotToManageList": "6AABG103M08,E2.19b"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1201788320590322", "MaskList": "ABE", "ProjectMaskList": "Quix 2", "LotToConfirmList": null, "LotToSplitList": "6AABG201M09", "PROJECTS": "E2.19a", "LotToManageList": "6AABG202M08,E2.19a for FA"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201830090104290", "MaskList": "ABF", "ProjectMaskList": "E2.13c", "LotToConfirmList": null, "LotToSplitList": "6AABF104M08", "PROJECTS": "E2.13c", "LotToManageList": "6AABG201X3B,E2.19a"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201899025933306", "MaskList": "ABG", "ProjectMaskList": "E2.19c", "LotToConfirmList": null, "LotToSplitList": "6AABF103M08", "PROJECTS": "E2.13b", "LotToManageList": "6AABG201M09,E2.19a"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201922419740872", "MaskList": "ABH", "ProjectMaskList": "E2.18", "LotToConfirmList": null, "LotToSplitList": "6AABF201M09", "PROJECTS": "E2.13a", "LotToManageList": "6AABF104M08,E2.13c"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201964818841249", "MaskList": "ABI", "ProjectMaskList": "E6.6", "LotToConfirmList": null, "LotToSplitList": "6AAAM102M11", "PROJECTS": "E2.14 TEL", "LotToManageList": "6AABF103M08,E2.13b"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1202129345004455", "MaskList": "ABJ", "ProjectMaskList": "E7.2", "LotToConfirmList": null, "LotToSplitList": "5AABE101M02", "PROJECTS": "Quix 2", "LotToManageList": "6AABF202M08,E2.13a - Bonding for FA C2N"}, {"requester_names_list": "Houssein EL DIRANI", "requester_gid_list": "1202135074352084", "MaskList": "ABK", "ProjectMaskList": "E2.23b", "LotToConfirmList": null, "LotToSplitList": "5AABD101M02", "PROJECTS": "Quix 1", "LotToManageList": "6AABF201X3B,E2.13a"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202444549987515", "MaskList": "ABL", "ProjectMaskList": "E2.12", "LotToConfirmList": null, "LotToSplitList": "4AABC201M19", "PROJECTS": "MAGNIFY WP2", "LotToManageList": "6AABF201M09,E2.13a"}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1202684057491700", "MaskList": "ABM", "ProjectMaskList": "E2.12 redesign", "LotToConfirmList": null, "LotToSplitList": "6AABB001M08", "PROJECTS": "redesign EIGR6p4", "LotToManageList": "6AAAM102M11,E2.14 TEL"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202907572860628", "MaskList": "ABN", "ProjectMaskList": "E2.20", "LotToConfirmList": null, "LotToSplitList": "6AAAY001M08", "PROJECTS": "EIGR8p4 second lot ", "LotToManageList": "5AABE101M02,<PERSON><PERSON>x 2"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203079565264253", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "5AAAX201M17", "PROJECTS": "LGT labs PD MTP", "LotToManageList": "5AABD101M02,Quix 1"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1203283724869310", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "5AAAW201M02", "PROJECTS": "Glider 3.1", "LotToManageList": "4AABC201M19,MAGNIFY WP2"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203601372370011", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "5AAAT101M03", "PROJECTS": "MPW33AB", "LotToManageList": "6AABB001M08,redesign EIGR6p4"}, {"requester_names_list": "Audie <PERSON>", "requester_gid_list": "1203873691444950", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "5AAAS101M16", "PROJECTS": "Dynamos 2", "LotToManageList": "6AAAY001M08,EIGR8p4 second lot "}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1203869571959179", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "5AAAR101M00", "PROJECTS": "ZTEP", "LotToManageList": "5AAAX201M17,LGT labs PD MTP"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204469536661025", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAO001M10", "PROJECTS": "<PERSON><PERSON><PERSON>", "LotToManageList": "5AAAW201M02,Glider 3.1"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204562481875818", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAN101M14", "PROJECTS": "EIGER 2.16", "LotToManageList": "5AAAT101M03,MPW33AB"}, {"requester_names_list": "Moustapha JAFFAL", "requester_gid_list": "1204562482451099", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAM201M11", "PROJECTS": "E2.14", "LotToManageList": "5AAAS101M16,Dynamos 2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1204325331381742", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "5AAAL101M03", "PROJECTS": "MPW32AB", "LotToManageList": "5AAAR101M00,ZTEP"}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1204701988903544", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAC201M09", "PROJECTS": "05-50 LER2", "LotToManageList": "6AAAO001M10,<PERSON><PERSON><PERSON>"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204950886823380", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAY202M08", "PROJECTS": "8.4 - LOT1", "LotToManageList": "6AAAN101M14,<PERSON>IG<PERSON> 2.16"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204968822616977", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAI001M08", "PROJECTS": "05-60_PAKH", "LotToManageList": "6AAAM201M11,E2.14"}, {"requester_names_list": "Viphretuo Mere", "requester_gid_list": "1204971721140670", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "4AAAH101M19", "PROJECTS": "05-61_LIGH", "LotToManageList": "5AAAL101M03,MPW32AB"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1205085908688191", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAA201M14", "PROJECTS": "05-46 EIGER 2.10", "LotToManageList": "6AAAC201M09,05-50 LER2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205137449747223", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AAAY202M08,8.4 - LOT1"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201278474361148", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AAAI001M08,05-60_PAKH"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205595234938764", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "4AAAH101M19,05-61_LIGH"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205709973073733", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AAAA201M14,05-46 EIGER 2.10"}, {"requester_names_list": "Yann K<PERSON>ZY<PERSON>", "requester_gid_list": "1204164213558726", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205847382524398", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1205874105867614", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1206225376712932", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206468476801011", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477227688", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477262721", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206660149483972", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660407180541", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408294625", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206660408413444", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408470048", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408540230", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207080700250290", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207080702899449", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}], "capacity_data_frame": [{"Lot Type": "Prototype", "Number of Lots": 6.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN800", "Priority": "Low", "Production date in": "Monday", "Projects": "MPW", "Task delay Update min (d)": 6.0, "Task advance update max (d)": 6.0, "max Lot reservation (d)": 14.0}, {"Lot Type": "Engineering", "Number of Lots": 6.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN350", "Priority": "Normal", "Production date in": "Tuesday", "Projects": "EIGER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Pre-production", "Number of Lots": 4.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 4.0, "Wafer number max": 25, "Technology": "AN200", "Priority": "High", "Production date in": "Wednesday", "Projects": "GLIDER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Proto-Engineering", "Number of Lots": 2.0, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": "Thursday", "Projects": "PHOTONIXFAB", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": "Friday", "Projects": "MAGNIFY", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "DYNAMOS", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "QPIC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "FEMTOCHIP", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "Dedicated run AN800", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "<PERSON><PERSON>", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "PhotonHub", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": NaN, "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": NaN, "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": "                                                     ", "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": NaN, "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}], "OP_data_frame": [{"Technology": "AN800", "Modules": "X1+Heater", "Pre-production(d)": 0, "Production(d)": 70.0, "Production-Rerun(d)": 53.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 1.0, "CS (d)": 12.0, "Grinding (d)": 14.0}, {"Technology": "AN800", "Modules": "X1+ILDB", "Pre-production(d)": 0, "Production(d)": 42.0, "Production-Rerun(d)": 30.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM EIGR", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater", "Pre-production(d)": 0, "Production(d)": 85.0, "Production-Rerun(d)": 64.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 70.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3 bonding", "Pre-production(d)": 0, "Production(d)": 60.0, "Production-Rerun(d)": 48.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 74.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+ILDB", "Pre-production(d)": 0, "Production(d)": 57.0, "Production-Rerun(d)": 45.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+ILDB", "Pre-production(d)": 0, "Production(d)": 77.0, "Production-Rerun(d)": 61.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 Bonding ", "Pre-production(d)": 7, "Production(d)": 25.0, "Production-Rerun(d)": 21.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDA", "Pre-production(d)": 7, "Production(d)": 32.0, "Production-Rerun(d)": 28.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDB", "Pre-production(d)": 7, "Production(d)": 39.0, "Production-Rerun(d)": 35.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + HEATER", "Pre-production(d)": 7, "Production(d)": 60.0, "Production-Rerun(d)": 47.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDA", "Pre-production(d)": 7, "Production(d)": 47.0, "Production-Rerun(d)": 43.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDB", "Pre-production(d)": 7, "Production(d)": 54.0, "Production-Rerun(d)": 50.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB +HEATER", "Pre-production(d)": 7, "Production(d)": 75.0, "Production-Rerun(d)": 62.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 104.0, "Production-Rerun(d)": 104.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 95.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1 bonding", "Pre-production(d)": 0, "Production(d)": 35.0, "Production-Rerun(d)": 35.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "SiN dep ", "Pre-production(d)": 0, "Production(d)": 7.0, "Production-Rerun(d)": 7.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN200", "Modules": "MX2 Bonding", "Pre-production(d)": 0, "Production(d)": 25.0, "Production-Rerun(d)": 21.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": " ", "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}], "ENG_data_frame": [{"Technology": "AN800", "Modules": "X1+Heater", "Pre-production(d)": 0, "Production(d)": 70.0, "Production-Rerun(d)": 53.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 1.0, "CS (d)": 12.0, "Grinding (d)": 14.0}, {"Technology": "AN800", "Modules": "X1+ILDB", "Pre-production(d)": 0, "Production(d)": 42.0, "Production-Rerun(d)": 30.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM EIGR", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater", "Pre-production(d)": 0, "Production(d)": 85.0, "Production-Rerun(d)": 64.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 70.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3 bonding", "Pre-production(d)": 0, "Production(d)": 60.0, "Production-Rerun(d)": 48.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 74.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+ILDB", "Pre-production(d)": 0, "Production(d)": 57.0, "Production-Rerun(d)": 45.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+ILDB", "Pre-production(d)": 0, "Production(d)": 77.0, "Production-Rerun(d)": 61.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 Bonding ", "Pre-production(d)": 7, "Production(d)": 33.0, "Production-Rerun(d)": 30.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDA", "Pre-production(d)": 7, "Production(d)": 37.0, "Production-Rerun(d)": 34.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDB", "Pre-production(d)": 7, "Production(d)": 40.0, "Production-Rerun(d)": 37.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + HEATER", "Pre-production(d)": 7, "Production(d)": 68.0, "Production-Rerun(d)": 56.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDA", "Pre-production(d)": 7, "Production(d)": 53.0, "Production-Rerun(d)": 50.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDB", "Pre-production(d)": 7, "Production(d)": 56.0, "Production-Rerun(d)": 53.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB +HEATER", "Pre-production(d)": 7, "Production(d)": 83.0, "Production-Rerun(d)": 71.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 104.0, "Production-Rerun(d)": 104.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 95.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1 bonding", "Pre-production(d)": 0, "Production(d)": 35.0, "Production-Rerun(d)": 35.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "SiN dep ", "Pre-production(d)": 0, "Production(d)": 7.0, "Production-Rerun(d)": 7.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN200", "Modules": "MX2 Bonding", "Pre-production(d)": 0, "Production(d)": 25.0, "Production-Rerun(d)": 21.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": " ", "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}], "AsanaProject_data_frame": [{"Asana project": "Mask assembly", "project gid": 1203927479554343.0, "Portfolio": "Prototype", "Portfolio gid": 1206397258164834.0, "Talos version": "1.0.0"}, {"Asana project": "RFQ-PO", "project gid": 1206397258493014.0, "Portfolio": "Engineering", "Portfolio gid": 1206397258164837.0, "Talos version": NaN}, {"Asana project": "PI", "project gid": 1206397258492997.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Testing", "project gid": 1206397258164847.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Data processing", "project gid": 1206397258493001.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Shipment", "project gid": 1206397258493005.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "CS", "project gid": 1206521529181046.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Grinding", "project gid": 1206521529181050.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": NaN, "project gid": NaN, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": NaN, "project gid": NaN, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": NaN, "project gid": NaN, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": ","}], "LotNumber_data_frame": [{"Technology": "AN800", "Technology code": "5A", "Lot Type": "Pre-production", "Lot Type code": 0.0, "Modules": "X1+Heater", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": "LGT code", "Unnamed: 10": "Technology", "Mask Number (3 digits)": "LGT Code ", "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": "XFAB Mask ref", "Lot type (1 digit)": "LGT code", "Unnamed: 16": "Lot type", "Run Number (2 digits)": "LGT Code ", "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": "LGT code", "Unnamed: 21": NaN, "Unnamed: 22": "Process step"}, {"Technology": "AN350", "Technology code": "6A", "Lot Type": "Prototype", "Lot Type code": 1.0, "Modules": "X1+Heater", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": "5A", "Unnamed: 10": "AN800", "Mask Number (3 digits)": "A", "Unnamed: 12": "A", "Unnamed: 13": "A", "Unnamed: 14": NaN, "Lot type (1 digit)": 0, "Unnamed: 16": "Pre-production", "Run Number (2 digits)": 0, "Unnamed: 18": 0.0, "Unnamed: 19": 0.0, "Process step (2digits)": 0, "Unnamed: 21": 0.0, "Unnamed: 22": "End"}, {"Technology": "AN200", "Technology code": "4A", "Lot Type": "Engineering", "Lot Type code": 2.0, "Modules": "X1+ILDB", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": "6A", "Unnamed: 10": "AN350", "Mask Number (3 digits)": 0, "Unnamed: 12": 0, "Unnamed: 13": 1, "Unnamed: 14": NaN, "Lot type (1 digit)": 1, "Unnamed: 16": "Prototype", "Run Number (2 digits)": 0, "Unnamed: 18": 0.0, "Unnamed: 19": 1.0, "Process step (2digits)": 0, "Unnamed: 21": 1.0, "Unnamed: 22": "X1"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": "Production", "Lot Type code": 3.0, "Modules": "X1+ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": 0, "Unnamed: 12": 0, "Unnamed: 13": 2, "Unnamed: 14": NaN, "Lot type (1 digit)": 2, "Unnamed: 16": "Engineering", "Run Number (2 digits)": 0, "Unnamed: 18": 0.0, "Unnamed: 19": 2.0, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": "X2"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": 3, "Unnamed: 16": "Production", "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": "HEATER"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X2 bonding", "Layer code": "X2B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "HEATER", "Layer code": "HET", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": "Example", "Mask Number (3 digits)": "05-044-1-00-2", "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "X1 RIB bonding", "Layer code": "1RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "X3 RIB bonding", "Layer code": "3RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "X3 RIB bonding", "Layer code": "3RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "X3 RIB bonding", "Layer code": "3RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+Heater", "Layer": "END", "Layer code": "M00", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+ILDB", "Layer": "END", "Layer code": "M01", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "END", "Layer code": "M02", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "END", "Layer code": "M03", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3 bonding", "Layer": "END", "Layer code": "M04", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "END", "Layer code": "M05", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "END", "Layer code": "M06", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "END", "Layer code": "M07", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 Bonding ", "Layer": "END", "Layer code": "M08", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "END", "Layer code": "M09", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "END", "Layer code": "M10", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "END", "Layer code": "M11", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "END", "Layer code": "M12", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "END", "Layer code": "M13", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "END", "Layer code": "M14", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater+LOCA-i", "Layer": "END", "Layer code": "M15", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater", "Layer": "END", "Layer code": "M16", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1 bonding", "Layer": "END", "Layer code": "M17", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "SiN dep", "Layer": "END", "Layer code": "M18", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX2 Bonding", "Layer": "END", "Layer code": "M19", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}], "Email_frame": [{"Requester": "<PERSON><PERSON>", "Acronym": "dav"}, {"Requester": "<PERSON>", "Acronym": "maz"}, {"Requester": "<PERSON>", "Acronym": "mwg"}, {"Requester": "<PERSON><PERSON>", "Acronym": "gbn"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "kal"}, {"Requester": "<PERSON>", "Acronym": "tat"}, {"Requester": "<PERSON>", "Acronym": "ast"}, {"Requester": "<PERSON><PERSON>", "Acronym": "fin"}, {"Requester": "<PERSON>", "Acronym": "tom"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "arc"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mar"}, {"Requester": "Ana Catarina <PERSON>", "Acronym": "acm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "ozp"}, {"Requester": "<PERSON>", "Acronym": "hry"}, {"Requester": "<PERSON>", "Acronym": "anb"}, {"Requester": "<PERSON>", "Acronym": "anf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "jeg"}, {"Requester": "<PERSON>", "Acronym": "jfc"}, {"Requester": "<PERSON> Yao", "Acronym": "cay"}, {"Requester": "<PERSON>", "Acronym": "piw"}, {"Requester": "<PERSON><PERSON>", "Acronym": "cob"}, {"Requester": "<PERSON>", "Acronym": "jdf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "plz"}, {"Requester": "<PERSON>", "Acronym": "sab"}, {"Requester": "<PERSON>", "Acronym": "stm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "asa"}, {"Requester": "<PERSON>", "Acronym": "axp"}, {"Requester": "<PERSON>", "Acronym": "gug"}, {"Requester": "Houssein EL DIRANI", "Acronym": "hod"}, {"Requester": "<PERSON><PERSON>", "Acronym": "dab"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON>", "Acronym": "abh"}, {"Requester": "<PERSON><PERSON>", "Acronym": "lde"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "alf"}, {"Requester": "<PERSON>", "Acronym": "cls"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "tol"}, {"Requester": "Audie <PERSON>", "Acronym": "aye"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mat"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "stc"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "git"}, {"Requester": "Moustapha JAFFAL", "Acronym": "moj"}, {"Requester": "<PERSON>", "Acronym": "anv"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Acronym": "abr"}, {"Requester": "Ulysse Fanda", "Acronym": "ulf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "chr"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "khk"}, {"Requester": "Viphretuo Mere", "Acronym": "vim"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "zba"}, {"Requester": "<PERSON>", "Acronym": "fov"}, {"Requester": "<PERSON><PERSON>", "Acronym": "jod"}, {"Requester": "<PERSON>", "Acronym": "frg"}, {"Requester": "<PERSON>", "Acronym": "joj"}, {"Requester": "Yann K<PERSON>ZY<PERSON>", "Acronym": "yak"}, {"Requester": "<PERSON>", "Acronym": "gab"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "swp"}, {"Requester": "<PERSON><PERSON>", "Acronym": "yil"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "sds"}, {"Requester": "<PERSON>", "Acronym": "mab"}, {"Requester": "<PERSON>", "Acronym": "ann"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "myl"}, {"Requester": "<PERSON>", "Acronym": "boz"}, {"Requester": "<PERSON>", "Acronym": "alb"}, {"Requester": "<EMAIL>", "Acronym": "adi"}, {"Requester": "<PERSON>", "Acronym": "lec"}, {"Requester": "<PERSON>", "Acronym": "dib"}], "project_data_frame": [{"list_projects_names": "5AAAE101X1B,05-56 MPW31 ILDB, start : 2024-02-21 , 67 days", "list_Start_ON": "2024-02-21", "list_Due_ON": "2024-04-28", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "67", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-02-21", "PIout": "2024-03-26", "PrePin": "", "PrePout": "", "Testout": "2024-03-27", "DataProcessOut": "2024-03-27", "ShipmentOut": "2024-04-03", "CSout": "2024-04-11", "GrindingOut": "2024-04-28", "list_requester": "<PERSON>", "WaferNumberList": "3"}, {"list_projects_names": "5AAAE101M02,05-56 MPW31, start : 2024-02-21 , 95 days", "list_Start_ON": "2024-02-21", "list_Due_ON": "2024-05-26", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "95", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-02-21", "PIout": "2024-05-02", "PrePin": "", "PrePout": "", "Testout": "2024-05-03", "DataProcessOut": "2024-05-04", "ShipmentOut": "2024-05-05", "CSout": "2024-05-12", "GrindingOut": "2024-05-26", "list_requester": "<PERSON>", "WaferNumberList": "3"}, {"list_projects_names": "5AAAF101M03,Rerun 05-43 MPW29a, start : 2024-02-22 , 78 days", "list_Start_ON": "2024-02-22", "list_Due_ON": "2024-05-10", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "78", "RFQPOin": "2024-02-13", "RFQPOout": "2024-02-20", "TOin": "", "TOout": "", "PIin": "2024-02-22", "PIout": "2024-04-15", "PrePin": "", "PrePout": "", "Testout": "2024-04-16", "DataProcessOut": "2024-04-16", "ShipmentOut": "2024-04-18", "CSout": "2024-04-26", "GrindingOut": "2024-05-10", "list_requester": "<PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "4AAAH101M19,05-61_LIGH, start : 2024-03-08 , 75 days", "list_Start_ON": "2024-03-08", "list_Due_ON": "2024-05-22", "list_corridor": "Prototype", "list_tech": "AN200", "list_cycleTime": "75", "RFQPOin": "2024-03-11", "RFQPOout": "2024-03-21", "TOin": "2024-03-08", "TOout": "2024-03-29", "PIin": "2024-03-29", "PIout": "2024-04-23", "PrePin": "", "PrePout": "", "Testout": "2024-04-24", "DataProcessOut": "2024-04-25", "ShipmentOut": "2024-04-26", "CSout": "2024-05-08", "GrindingOut": "2024-05-22", "list_requester": "<PERSON>", "WaferNumberList": "15"}, {"list_projects_names": "6AAAI001M08,05-60_PAKH, start : 2024-03-18 , 86 days", "list_Start_ON": "2024-03-18", "list_Due_ON": "2024-06-12", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "86", "RFQPOin": "", "RFQPOout": "", "TOin": "2024-03-18", "TOout": "2024-03-21", "PIin": "2024-04-04", "PIout": "2024-05-14", "PrePin": "", "PrePout": "", "Testout": "2024-05-15", "DataProcessOut": "2024-05-16", "ShipmentOut": "2024-05-17", "CSout": "2024-05-29", "GrindingOut": "2024-06-12", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "5AAAL101M03,MPW32AB, start : 2024-04-01 , 140 days", "list_Start_ON": "2024-04-01", "list_Due_ON": "2024-08-19", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "140", "RFQPOin": "2024-03-27", "RFQPOout": "2024-04-09", "TOin": "2024-04-01", "TOout": "2024-04-17", "PIin": "2024-04-17", "PIout": "2024-07-21", "PrePin": "", "PrePout": "", "Testout": "2024-07-22", "DataProcessOut": "2024-07-23", "ShipmentOut": "2024-07-24", "CSout": "2024-08-05", "GrindingOut": "2024-08-19", "list_requester": "<PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "6AAAN101M14,EIGER 2.16, start : 2024-05-04 , 105 days", "list_Start_ON": "2024-05-04", "list_Due_ON": "2024-08-17", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "105", "RFQPOin": "2024-05-04", "RFQPOout": "2024-05-14", "TOin": "2024-05-04", "TOout": "2024-05-22", "PIin": "2024-05-22", "PIout": "2024-08-12", "PrePin": "2024-04-24", "PrePout": "2024-05-02", "Testout": "2024-08-15", "DataProcessOut": "2024-08-16", "ShipmentOut": "2024-08-17", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AAAO001M10,<PERSON><PERSON><PERSON>, start : 2024-06-20 , 93 days", "list_Start_ON": "2024-06-20", "list_Due_ON": "2024-09-21", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "93", "RFQPOin": "2024-06-20", "RFQPOout": "2024-06-30", "TOin": "2024-06-20", "TOout": "2024-07-08", "PIin": "2024-07-08", "PIout": "2024-08-23", "PrePin": "2024-05-21", "PrePout": "2024-05-28", "Testout": "2024-08-24", "DataProcessOut": "2024-08-25", "ShipmentOut": "2024-08-26", "CSout": "2024-09-07", "GrindingOut": "2024-09-21", "list_requester": "<PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "5AAAS101M16,Dynamos 2, start : 2024-07-03 , 142 days", "list_Start_ON": "2024-07-03", "list_Due_ON": "2024-11-22", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "142", "RFQPOin": "2024-07-03", "RFQPOout": "2024-07-13", "TOin": "2024-07-03", "TOout": "2024-07-21", "PIin": "2024-07-21", "PIout": "2024-10-24", "PrePin": "", "PrePout": "", "Testout": "2024-10-25", "DataProcessOut": "2024-10-26", "ShipmentOut": "2024-10-27", "CSout": "2024-11-08", "GrindingOut": "2024-11-22", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "10"}, {"list_projects_names": "6AABB001M08,redesign EIGR6p4, start : 2024-04-12 , 35 days", "list_Start_ON": "2024-04-12", "list_Due_ON": "2024-05-17", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "35", "RFQPOin": "2024-04-07", "RFQPOout": "2024-04-14", "TOin": "2024-04-12", "TOout": "2024-04-26", "PIin": "2024-04-24", "PIout": "2024-05-13", "PrePin": "2024-04-15", "PrePout": "2024-04-22", "Testout": "2024-05-14", "DataProcessOut": "2024-05-15", "ShipmentOut": "2024-05-17", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "5AABD101M02,Quix 1, start : 2024-04-18 , 132 days", "list_Start_ON": "2024-04-18", "list_Due_ON": "2024-08-28", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "132", "RFQPOin": "2024-04-18", "RFQPOout": "2024-04-28", "TOin": "2024-04-18", "TOout": "2024-05-06", "PIin": "2024-05-06", "PIout": "2024-07-30", "PrePin": "", "PrePout": "", "Testout": "2024-07-31", "DataProcessOut": "2024-08-01", "ShipmentOut": "2024-08-02", "CSout": "2024-08-14", "GrindingOut": "2024-08-28", "list_requester": "<PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "5AABE101M02,Quix 2, start : 2024-04-25 , 132 days", "list_Start_ON": "2024-04-25", "list_Due_ON": "2024-09-04", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "132", "RFQPOin": "2024-04-25", "RFQPOout": "2024-05-05", "TOin": "2024-04-25", "TOout": "2024-05-13", "PIin": "2024-05-13", "PIout": "2024-08-06", "PrePin": "", "PrePout": "", "Testout": "2024-08-07", "DataProcessOut": "2024-08-08", "ShipmentOut": "2024-08-09", "CSout": "2024-08-21", "GrindingOut": "2024-09-04", "list_requester": "<PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "6AAAM102M11,E2.14 TEL, start : 2024-05-08 , 57 days", "list_Start_ON": "2024-05-08", "list_Due_ON": "2024-07-04", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "57", "RFQPOin": "2024-05-06", "RFQPOout": "2024-05-16", "TOin": "", "TOout": "", "PIin": "2024-05-08", "PIout": "2024-07-01", "PrePin": "2024-02-12", "PrePout": "2024-02-18", "Testout": "2024-07-02", "DataProcessOut": "2024-07-03", "ShipmentOut": "2024-07-04", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "6AABF103M08,E2.13b, start : 2024-05-02 , 33 days", "list_Start_ON": "2024-05-02", "list_Due_ON": "2024-06-04", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "33", "RFQPOin": "2024-05-02", "RFQPOout": "2024-05-12", "TOin": "", "TOout": "", "PIin": "2024-05-02", "PIout": "2024-05-30", "PrePin": "2024-04-24", "PrePout": "2024-05-02", "Testout": "2024-06-02", "DataProcessOut": "2024-06-03", "ShipmentOut": "2024-06-04", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABF104M08,E2.13c, start : 2024-05-02 , 33 days", "list_Start_ON": "2024-05-02", "list_Due_ON": "2024-06-04", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "33", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-05-02", "PIout": "2024-05-30", "PrePin": "2024-04-24", "PrePout": "2024-05-02", "Testout": "2024-06-02", "DataProcessOut": "2024-06-03", "ShipmentOut": "2024-06-04", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABG103M08,E2.19b, start : 2024-05-15 , 33 days", "list_Start_ON": "2024-05-15", "list_Due_ON": "2024-06-17", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "33", "RFQPOin": "2024-05-16", "RFQPOout": "2024-05-26", "TOin": "", "TOout": "", "PIin": "2024-05-15", "PIout": "2024-06-12", "PrePin": "2024-04-24", "PrePout": "2024-05-02", "Testout": "2024-06-15", "DataProcessOut": "2024-06-16", "ShipmentOut": "2024-06-17", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABG104M08,E2.19c, start : 2024-05-15 , 33 days", "list_Start_ON": "2024-05-15", "list_Due_ON": "2024-06-17", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "33", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-05-15", "PIout": "2024-06-12", "PrePin": "2024-04-24", "PrePout": "2024-05-02", "Testout": "2024-06-15", "DataProcessOut": "2024-06-16", "ShipmentOut": "2024-06-17", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AAAA201M14,05-46 EIGER 2.10, start : 2023-11-23 , 173 days", "list_Start_ON": "2023-11-23", "list_Due_ON": "2024-05-14", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "173", "RFQPOin": "2023-11-23", "RFQPOout": "2023-12-03", "TOin": "2023-11-23", "TOout": "2023-12-14", "PIin": "2023-12-14", "PIout": "2024-05-10", "PrePin": "", "PrePout": "", "Testout": "2024-05-12", "DataProcessOut": "2024-05-13", "ShipmentOut": "2024-05-14", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "10"}, {"list_projects_names": "6AAAC201M09,05-50 LER2, start : 2023-12-06 , 166 days", "list_Start_ON": "2023-12-06", "list_Due_ON": "2024-05-20", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "166", "RFQPOin": "2023-12-09", "RFQPOout": "2023-12-19", "TOin": "2023-12-06", "TOout": "2023-12-27", "PIin": "2023-12-27", "PIout": "2024-05-20", "PrePin": "", "PrePout": "", "Testout": "2024-05-03", "DataProcessOut": "2024-05-04", "ShipmentOut": "2024-05-05", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AAAD201M11,05-54 EIGER 2.11, start : 2023-12-12 , 129 days", "list_Start_ON": "2023-12-12", "list_Due_ON": "2024-04-19", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "129", "RFQPOin": "2023-12-15", "RFQPOout": "2023-12-25", "TOin": "2023-12-12", "TOout": "2024-01-02", "PIin": "2024-01-02", "PIout": "2024-03-18", "PrePin": "", "PrePout": "", "Testout": "2024-03-18", "DataProcessOut": "2024-04-19", "ShipmentOut": "2024-03-21", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "2"}, {"list_projects_names": "6AAAM201M11,EIGER 2.14, start : 2024-05-08 , 80 days", "list_Start_ON": "2024-05-08", "list_Due_ON": "2024-07-27", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "80", "RFQPOin": "2024-04-20", "RFQPOout": "2024-04-30", "TOin": "", "TOout": "", "PIin": "2024-05-08", "PIout": "2024-07-22", "PrePin": "2024-04-24", "PrePout": "2024-05-02", "Testout": "2024-07-25", "DataProcessOut": "2024-07-26", "ShipmentOut": "2024-07-27", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "5AAAR101M00,ZTEP, start : 2024-06-21 , 91 days", "list_Start_ON": "2024-06-21", "list_Due_ON": "2024-09-20", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "91", "RFQPOin": "2024-06-21", "RFQPOout": "2024-07-01", "TOin": "2024-06-21", "TOout": "2024-07-09", "PIin": "2024-07-09", "PIout": "2024-09-17", "PrePin": "", "PrePout": "", "Testout": "2024-09-18", "DataProcessOut": "2024-09-19", "ShipmentOut": "2024-09-20", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON> Yao", "WaferNumberList": "9"}, {"list_projects_names": "5AAAT101M03,MPW33AB, start : 2024-07-12 , 144 days", "list_Start_ON": "2024-07-12", "list_Due_ON": "2024-12-03", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "144", "RFQPOin": "2024-07-12", "RFQPOout": "2024-07-22", "TOin": "2024-07-12", "TOout": "2024-07-30", "PIin": "2024-07-30", "PIout": "2024-11-04", "PrePin": "", "PrePout": "", "Testout": "2024-11-05", "DataProcessOut": "2024-11-06", "ShipmentOut": "2024-11-07", "CSout": "2024-11-19", "GrindingOut": "2024-12-03", "list_requester": "<PERSON>", "WaferNumberList": "6"}, {"list_projects_names": "5AAAW201M02,Glider 3.1, start : 2024-05-23 , 134 days", "list_Start_ON": "2024-05-23", "list_Due_ON": "2024-10-04", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "134", "RFQPOin": "2024-05-23", "RFQPOout": "2024-06-02", "TOin": "2024-05-23", "TOout": "2024-06-10", "PIin": "2024-06-10", "PIout": "2024-09-03", "PrePin": "", "PrePout": "", "Testout": "2024-09-06", "DataProcessOut": "2024-09-07", "ShipmentOut": "2024-09-08", "CSout": "2024-09-20", "GrindingOut": "2024-10-04", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "5AAAX201M17,LGT labs PD MTP, start : 2024-04-05 , 59 days", "list_Start_ON": "2024-04-05", "list_Due_ON": "2024-06-03", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "59", "RFQPOin": "2024-04-08", "RFQPOout": "2024-04-18", "TOin": "2024-04-05", "TOout": "2024-04-26", "PIin": "2024-04-26", "PIout": "2024-05-31", "PrePin": "", "PrePout": "", "Testout": "2024-06-01", "DataProcessOut": "2024-06-02", "ShipmentOut": "2024-06-03", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "10"}, {"list_projects_names": "6AAAY001M08,EIGR8p4 second lot , start : 2024-04-11 , 65 days", "list_Start_ON": "2024-04-11", "list_Due_ON": "2024-06-15", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "65", "RFQPOin": "2024-04-11", "RFQPOout": "2024-04-21", "TOin": "2024-04-11", "TOout": "2024-04-29", "PIin": "2024-04-29", "PIout": "2024-06-10", "PrePin": "", "PrePout": "", "Testout": "2024-06-13", "DataProcessOut": "2024-06-14", "ShipmentOut": "2024-06-15", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "4AABC201M19,MAGNIFY WP2, start : 2024-04-14 , 46 days", "list_Start_ON": "2024-04-14", "list_Due_ON": "2024-05-30", "list_corridor": "Engineering", "list_tech": "AN200", "list_cycleTime": "46", "RFQPOin": "2024-04-14", "RFQPOout": "2024-04-24", "TOin": "2024-04-14", "TOout": "2024-05-02", "PIin": "2024-05-02", "PIout": "2024-05-27", "PrePin": "", "PrePout": "", "Testout": "2024-05-28", "DataProcessOut": "2024-05-29", "ShipmentOut": "2024-05-30", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "10"}, {"list_projects_names": "6AAAA201ILA,05-46 EIGER 2.10,ILDA, start : 2023-11-23 , 139 days", "list_Start_ON": "2023-11-23", "list_Due_ON": "2024-04-10", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "139", "RFQPOin": "2023-11-23", "RFQPOout": "2023-12-03", "TOin": "2023-11-23", "TOout": "2023-12-14", "PIin": "2023-12-14", "PIout": "2024-04-05", "PrePin": "", "PrePout": "", "Testout": "2024-04-09", "DataProcessOut": "2024-04-10", "ShipmentOut": "2024-04-08", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "8"}, {"list_projects_names": "6AAAY202M08,8.4 - LOT1, start : 2024-02-26 , 63 days", "list_Start_ON": "2024-02-26", "list_Due_ON": "2024-04-29", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "63", "RFQPOin": "2024-02-15", "RFQPOout": "2024-02-25", "TOin": "", "TOout": "", "PIin": "2024-02-26", "PIout": "2024-04-29", "PrePin": "", "PrePout": "", "Testout": "2024-04-15", "DataProcessOut": "2024-04-16", "ShipmentOut": "2024-04-17", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "10"}, {"list_projects_names": "6AAAY203M08,8.4 - LOT1,Splitted lot, start : 2024-02-26 , 50 days", "list_Start_ON": "2024-02-26", "list_Due_ON": "2024-04-16", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "50", "RFQPOin": "2024-02-15", "RFQPOout": "2024-02-25", "TOin": "", "TOout": "", "PIin": "2024-02-26", "PIout": "2024-04-16", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-03-19", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "15"}, {"list_projects_names": "6AABF201M09,E2.13a, start : 2024-04-11 , 70 days", "list_Start_ON": "2024-04-11", "list_Due_ON": "2024-06-20", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "70", "RFQPOin": "2024-04-14", "RFQPOout": "2024-04-24", "TOin": "2024-04-11", "TOout": "2024-04-22", "PIin": "2024-05-02", "PIout": "2024-06-17", "PrePin": "2024-04-24", "PrePout": "2024-05-02", "Testout": "2024-06-18", "DataProcessOut": "2024-06-19", "ShipmentOut": "2024-06-20", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3"}, {"list_projects_names": "6AABF201X3B,E2.13a,X3 bonding, start : 2024-04-11 , 74 days", "list_Start_ON": "2024-04-11", "list_Due_ON": "2024-06-24", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "74", "RFQPOin": "2024-04-14", "RFQPOout": "2024-04-24", "TOin": "2024-04-11", "TOout": "2024-04-22", "PIin": "2024-05-10", "PIout": "2024-06-24", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-06-24", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "19"}, {"list_projects_names": "6AABF202M08,E2.13a - Bonding for FA C2N,Splitted lot, start : 2024-04-11 , 74 days", "list_Start_ON": "2024-04-11", "list_Due_ON": "2024-06-24", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "74", "RFQPOin": "2024-04-14", "RFQPOout": "2024-04-24", "TOin": "2024-04-11", "TOout": "2024-04-22", "PIin": "2024-05-10", "PIout": "2024-06-24", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-06-24", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "3"}, {"list_projects_names": "6AABG201M09,E2.19a, start : 2024-04-11 , 81 days", "list_Start_ON": "2024-04-11", "list_Due_ON": "2024-07-01", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "81", "RFQPOin": "2024-04-27", "RFQPOout": "2024-05-07", "TOin": "2024-04-11", "TOout": "2024-04-25", "PIin": "2024-05-15", "PIout": "2024-06-28", "PrePin": "", "PrePout": "", "Testout": "2024-06-29", "DataProcessOut": "2024-06-30", "ShipmentOut": "2024-07-01", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3"}, {"list_projects_names": "6AABG201X3B,E2.19a,X3 bonding, start : 2024-04-11 , 3742 days", "list_Start_ON": "2024-04-11", "list_Due_ON": "2034-07-10", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "3742", "RFQPOin": "2024-04-27", "RFQPOout": "2024-05-07", "TOin": "2024-04-11", "TOout": "2024-04-25", "PIin": "2024-05-31", "PIout": "2034-07-10", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-07-11", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "19"}, {"list_projects_names": "6AABG202M08,E2.19a for FA,Splitted lot, start : 2024-04-11 , 91 days", "list_Start_ON": "2024-04-11", "list_Due_ON": "2024-07-11", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "91", "RFQPOin": "2024-04-27", "RFQPOout": "2024-05-07", "TOin": "2024-04-11", "TOout": "2024-04-25", "PIin": "2024-05-31", "PIout": "2024-07-10", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-07-11", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "3"}, {"list_projects_names": "6AAAB202M09,LER DEV, start : 2024-04-29 , 47 days", "list_Start_ON": "2024-04-29", "list_Due_ON": "2024-06-15", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "47", "RFQPOin": "2024-04-18", "RFQPOout": "2024-04-28", "TOin": "", "TOout": "", "PIin": "2024-04-29", "PIout": "2024-06-10", "PrePin": "", "PrePout": "", "Testout": "2024-06-13", "DataProcessOut": "2024-06-14", "ShipmentOut": "2024-06-15", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABH201M09,E2.15 TEL, start : 2024-07-17 , 49 days", "list_Start_ON": "2024-07-17", "list_Due_ON": "2024-09-04", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "49", "RFQPOin": "2024-06-29", "RFQPOout": "2024-07-09", "TOin": "", "TOout": "", "PIin": "2024-07-17", "PIout": "2024-08-30", "PrePin": "", "PrePout": "", "Testout": "2024-09-02", "DataProcessOut": "2024-09-03", "ShipmentOut": "2024-09-04", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABH202M09,E2.15a, start : 2024-07-17 , 46 days", "list_Start_ON": "2024-07-17", "list_Due_ON": "2024-09-01", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "46", "RFQPOin": "2024-07-11", "RFQPOout": "2024-07-21", "TOin": "", "TOout": "", "PIin": "2024-07-17", "PIout": "2024-08-27", "PrePin": "", "PrePout": "", "Testout": "2024-08-30", "DataProcessOut": "2024-08-31", "ShipmentOut": "2024-09-01", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABH203M09,E2.17, start : 2024-09-11 , 46 days", "list_Start_ON": "2024-09-11", "list_Due_ON": "2024-10-27", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "46", "RFQPOin": "2024-08-31", "RFQPOout": "2024-09-10", "TOin": "", "TOout": "", "PIin": "2024-09-11", "PIout": "2024-10-22", "PrePin": "", "PrePout": "", "Testout": "2024-10-25", "DataProcessOut": "2024-10-26", "ShipmentOut": "2024-10-27", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABH204M09,E2.18, start : 2024-11-06 , 46 days", "list_Start_ON": "2024-11-06", "list_Due_ON": "2024-12-22", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "46", "RFQPOin": "2024-10-24", "RFQPOout": "2024-11-03", "TOin": "", "TOout": "", "PIin": "2024-11-06", "PIout": "2024-12-17", "PrePin": "", "PrePout": "", "Testout": "2024-12-20", "DataProcessOut": "2024-12-21", "ShipmentOut": "2024-12-22", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABI201M09,E6.6, start : 2024-05-18 , 67 days", "list_Start_ON": "2024-05-18", "list_Due_ON": "2024-07-24", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "67", "RFQPOin": "2024-05-18", "RFQPOout": "2024-05-28", "TOin": "2024-05-18", "TOout": "2024-06-05", "PIin": "2024-06-05", "PIout": "2024-07-19", "PrePin": "", "PrePout": "", "Testout": "2024-07-22", "DataProcessOut": "2024-07-23", "ShipmentOut": "2024-07-24", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABI202M09,E6.6, start : 2024-06-05 , 49 days", "list_Start_ON": "2024-06-05", "list_Due_ON": "2024-07-24", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "49", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-05", "PIout": "2024-07-19", "PrePin": "", "PrePout": "", "Testout": "2024-07-22", "DataProcessOut": "2024-07-23", "ShipmentOut": "2024-07-24", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABI203M09,E6.6, start : 2024-06-05 , 49 days", "list_Start_ON": "2024-06-05", "list_Due_ON": "2024-07-24", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "49", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-05", "PIout": "2024-07-19", "PrePin": "", "PrePout": "", "Testout": "2024-07-22", "DataProcessOut": "2024-07-23", "ShipmentOut": "2024-07-24", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABJ201M14,E7.2, start : 2024-06-08 , 113 days", "list_Start_ON": "2024-06-08", "list_Due_ON": "2024-09-29", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "113", "RFQPOin": "2024-06-08", "RFQPOout": "2024-06-18", "TOin": "2024-06-08", "TOout": "2024-06-26", "PIin": "2024-06-26", "PIout": "2024-09-24", "PrePin": "", "PrePout": "", "Testout": "2024-09-27", "DataProcessOut": "2024-09-28", "ShipmentOut": "2024-09-29", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABJ202M14,E7.2, start : 2024-06-26 , 95 days", "list_Start_ON": "2024-06-26", "list_Due_ON": "2024-09-29", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "95", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-26", "PIout": "2024-09-24", "PrePin": "", "PrePout": "", "Testout": "2024-09-27", "DataProcessOut": "2024-09-28", "ShipmentOut": "2024-09-29", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABK201M14,E2.23a, start : 2024-09-23 , 96 days", "list_Start_ON": "2024-09-23", "list_Due_ON": "2024-12-28", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "96", "RFQPOin": "2024-09-05", "RFQPOout": "2024-09-15", "TOin": "", "TOout": "", "PIin": "2024-09-23", "PIout": "2024-12-23", "PrePin": "", "PrePout": "", "Testout": "2024-12-26", "DataProcessOut": "2024-12-27", "ShipmentOut": "2024-12-28", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABK202M14,E2.23b, start : 2024-09-23 , 96 days", "list_Start_ON": "2024-09-23", "list_Due_ON": "2024-12-28", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "96", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-23", "PIout": "2024-12-23", "PrePin": "", "PrePout": "", "Testout": "2024-12-26", "DataProcessOut": "2024-12-27", "ShipmentOut": "2024-12-28", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABL201M09,E2.12, start : 2024-07-01 , 68 days", "list_Start_ON": "2024-07-01", "list_Due_ON": "2024-09-07", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "68", "RFQPOin": "2024-07-01", "RFQPOout": "2024-07-11", "TOin": "2024-07-01", "TOout": "2024-07-19", "PIin": "2024-07-19", "PIout": "2024-09-02", "PrePin": "", "PrePout": "", "Testout": "2024-09-05", "DataProcessOut": "2024-09-06", "ShipmentOut": "2024-09-07", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABM201M09,E2.12 redesign, start : 2024-09-30 , 68 days", "list_Start_ON": "2024-09-30", "list_Due_ON": "2024-12-07", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "68", "RFQPOin": "2024-09-30", "RFQPOout": "2024-10-10", "TOin": "2024-09-30", "TOout": "2024-10-18", "PIin": "2024-10-18", "PIout": "2024-12-02", "PrePin": "", "PrePout": "", "Testout": "2024-12-05", "DataProcessOut": "2024-12-06", "ShipmentOut": "2024-12-07", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABN201M09,E2.20, start : 2024-10-31 , 67 days", "list_Start_ON": "2024-10-31", "list_Due_ON": "2025-01-06", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "67", "RFQPOin": "2024-10-31", "RFQPOout": "2024-11-10", "TOin": "2024-10-31", "TOout": "2024-11-18", "PIin": "2024-11-18", "PIout": "2025-01-01", "PrePin": "", "PrePout": "", "Testout": "2025-01-04", "DataProcessOut": "2025-01-05", "ShipmentOut": "2025-01-06", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AABN202M09,E2.20, start : 2024-11-18 , 49 days", "list_Start_ON": "2024-11-18", "list_Due_ON": "2025-01-06", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "49", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-11-18", "PIout": "2025-01-01", "PrePin": "", "PrePout": "", "Testout": "2025-01-04", "DataProcessOut": "2025-01-05", "ShipmentOut": "2025-01-06", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AAAC202M09,TW13 Qualification, start : 2024-06-03 , 61 days", "list_Start_ON": "2024-06-03", "list_Due_ON": "2024-08-03", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "61", "RFQPOin": "2024-05-23", "RFQPOout": "2024-06-02", "TOin": "", "TOout": "", "PIin": "2024-06-03", "PIout": "2024-07-29", "PrePin": "", "PrePout": "", "Testout": "2024-08-01", "DataProcessOut": "2024-08-02", "ShipmentOut": "2024-08-03", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AAAC203M09,TW13 Qualification, start : 2024-06-03 , 61 days", "list_Start_ON": "2024-06-03", "list_Due_ON": "2024-08-03", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "61", "RFQPOin": "", "RFQPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-03", "PIout": "2024-07-29", "PrePin": "", "PrePout": "", "Testout": "2024-08-01", "DataProcessOut": "2024-08-02", "ShipmentOut": "2024-08-03", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AAAC204M09,TW13 Qualification rerun, start : 2024-07-04 , 60 days", "list_Start_ON": "2024-07-04", "list_Due_ON": "2024-09-02", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "60", "RFQPOin": "2024-06-23", "RFQPOout": "2024-07-03", "TOin": "", "TOout": "", "PIin": "2024-07-04", "PIout": "2024-08-28", "PrePin": "", "PrePout": "", "Testout": "2024-08-31", "DataProcessOut": "2024-09-01", "ShipmentOut": "2024-09-02", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}, {"list_projects_names": "6AAAC205M09,LER3, start : 2024-08-08 , 76 days", "list_Start_ON": "2024-08-08", "list_Due_ON": "2024-10-23", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "76", "RFQPOin": "2024-07-28", "RFQPOout": "2024-08-07", "TOin": "", "TOout": "", "PIin": "2024-08-08", "PIout": "2024-10-18", "PrePin": "", "PrePout": "", "Testout": "2024-10-21", "DataProcessOut": "2024-10-22", "ShipmentOut": "2024-10-23", "CSout": "", "GrindingOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25"}]}