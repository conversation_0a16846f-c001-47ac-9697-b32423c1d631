{"df_asana": [{"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "464436269489794", "MaskList": "AAA", "ProjectMaskList": "05-46 EIGER 2.10", "LotToConfirmList": "5AAFD101M00,ZERMATT-RUN3", "LotToSplitList": "6AABS207M00", "PROJECTS": "MERLIN5 RERUN4", "LotToManageList": "6AABS207M00,MERLIN5 RERUN4"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "467496932920604", "MaskList": "AAB", "ProjectMaskList": "LER", "LotToConfirmList": "6AAFB101M00,NGI FOG_Spiral Proto", "LotToSplitList": "6AAFC101M00", "PROJECTS": "<PERSON><PERSON>", "LotToManageList": "5AAFD101M00,ZERMATT-RUN3"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "489733785249552", "MaskList": "AAC", "ProjectMaskList": "METROLOGY_OPT DOE", "LotToConfirmList": "5AAFA101M00,NGI_FOG NGI_Circuit Proto", "LotToSplitList": "6AAEY101M00", "PROJECTS": "JURA-LN1", "LotToManageList": "6AAFC101M00,Luci"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "513842320205279", "MaskList": "AAD", "ProjectMaskList": "05-54 EIGER 2.11 UCUT", "LotToConfirmList": "5AAEZ101M00,NGI_FOG NGI_Circuit Proto", "LotToSplitList": "5AAEX101M00", "PROJECTS": "Proto", "LotToManageList": "6AAFB101M00,NGI FOG_Spiral Proto"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "794540907829992", "MaskList": "AAE", "ProjectMaskList": "05-56 MPW31 ILDB", "LotToConfirmList": null, "LotToSplitList": "6AACV204M00", "PROJECTS": "SCTR_PVD_DOE", "LotToManageList": "5AAFA101M00,NGI_FOG NGI_Circuit Proto"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "869201729673462", "MaskList": "AAF", "ProjectMaskList": "MPW-AN800-29", "LotToConfirmList": null, "LotToSplitList": "6AAEW101M00", "PROJECTS": "ETV6.4", "LotToManageList": "5AAEZ101M00,NGI_FOG NGI_Circuit Proto"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "669147563988443", "MaskList": "AAG", "ProjectMaskList": "05-55 UCSD", "LotToConfirmList": null, "LotToSplitList": "6AAEV101M00", "PROJECTS": "TNG2", "LotToManageList": "6AAEY101M00,JURA-LN1"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1131443696074648", "MaskList": "AAH", "ProjectMaskList": "05-61_LIGH", "LotToConfirmList": null, "LotToSplitList": "6AAEU101M00", "PROJECTS": "TNG3", "LotToManageList": "5AAEX101M00,Proto"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1125291682985446", "MaskList": "AAI", "ProjectMaskList": "05-60_PAKH", "LotToConfirmList": null, "LotToSplitList": "6AAET101M00", "PROJECTS": "TV7.1", "LotToManageList": "6AACV204M00,SCTR_PVD_DOE"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1160066995528264", "MaskList": "AAL", "ProjectMaskList": "MPW32AB", "LotToConfirmList": null, "LotToSplitList": "6AAES201M00", "PROJECTS": "AN350_X0_IMPLEMENTATION", "LotToManageList": "6AAEW101M00,ETV6.4"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1171139149669535", "MaskList": "AAM", "ProjectMaskList": "E2.14 TEL", "LotToConfirmList": null, "LotToSplitList": "6AACU204M00", "PROJECTS": "FAU_WP1.6.1_SiN_Plate_Technology", "LotToManageList": "6AAEV101M00,TNG2"}, {"requester_names_list": "Ana Catarina <PERSON>", "requester_gid_list": "1183107046965382", "MaskList": "AAN", "ProjectMaskList": "HAD1", "LotToConfirmList": null, "LotToSplitList": "6AADD201M00", "PROJECTS": "2nd Metal Layer - Shortloop", "LotToManageList": "6AAEU101M00,TNG3"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1191400555194352", "MaskList": "AAO", "ProjectMaskList": "1534_24025", "LotToConfirmList": null, "LotToSplitList": "6AABP201M11", "PROJECTS": "E2.18 Wolverine3 TRIM.", "LotToManageList": "6AAET101M00,TV7.1"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1193626179934205", "MaskList": "AAR", "ProjectMaskList": "<PERSON><PERSON><PERSON><PERSON>", "LotToConfirmList": null, "LotToSplitList": "6AABO201M11", "PROJECTS": "E2.17 Wolverine3 TRIM.", "LotToManageList": "6AAES201M00,AN350_X0_IMPLEMENTATION"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199110939357003", "MaskList": "AAT", "ProjectMaskList": "MPW-AN800-33AB", "LotToConfirmList": null, "LotToSplitList": "6AAED203M00", "PROJECTS": "DV41_LER DOE LER OPT LOT2", "LotToManageList": "6AACU204M00,FAU_WP1.6.1_SiN_Plate_Technology"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199394325037760", "MaskList": "AAX", "ProjectMaskList": "LGT labs PD MTP", "LotToConfirmList": null, "LotToSplitList": "6AAEQ201M00", "PROJECTS": "NEW_HEATER DOE LOT", "LotToManageList": "6AADD201M00,2nd Metal Layer - Shortloop"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1199990271835482", "MaskList": "AAY", "ProjectMaskList": "8.4 - LOT1", "LotToConfirmList": null, "LotToSplitList": "6AAEI203M00", "PROJECTS": "FAU_WP1.8.0_13um_LPTEOS_TOX", "LotToManageList": "6AABP201M11,E2.18 Wolverine3 TRIM."}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1200295708067936", "MaskList": "ABB", "ProjectMaskList": "redesign EIGR6p4", "LotToConfirmList": null, "LotToSplitList": "6AAEO101M00", "PROJECTS": "MPW-AN350-05", "LotToManageList": "6AABO201M11,E2.17 Wolverine3 TRIM."}, {"requester_names_list": "<PERSON> Yao", "requester_gid_list": "1200692689203662", "MaskList": "ABC", "ProjectMaskList": "1668_16160", "LotToConfirmList": null, "LotToSplitList": "5AAEN101M00", "PROJECTS": "MPW-AN800-37", "LotToManageList": "6AAED203M00,DV41_LER DOE LER OPT LOT2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201328860961631", "MaskList": "ABD", "ProjectMaskList": "1037_21085_1", "LotToConfirmList": null, "LotToSplitList": "5AAEL101M00", "PROJECTS": "UGENT1 Proto", "LotToManageList": "6AAEQ201M00,NEW_HEATER DOE LOT"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201466301932731", "MaskList": "ABE", "ProjectMaskList": "1037_21085_2", "LotToConfirmList": null, "LotToSplitList": "5AAEK101M00", "PROJECTS": "femto-icomb run1", "LotToManageList": "6AAEI203M00,FAU_WP1.8.0_13um_LPTEOS_TOX"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201752762206320", "MaskList": "ABF", "ProjectMaskList": "E2.13 CH4B", "LotToConfirmList": null, "LotToSplitList": "6AAEI201M00", "PROJECTS": "FAU_WP1.9_RI_OXIDES", "LotToManageList": "6AAEO101M00,MPW-AN350-05"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1201788320590322", "MaskList": "ABG", "ProjectMaskList": "E2.19a", "LotToConfirmList": null, "LotToSplitList": "6AAAC203M00", "PROJECTS": "METROLOGY_OPT DOE", "LotToManageList": "5AAEN101M00,MPW-AN800-37"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201830090104290", "MaskList": "ABJ", "ProjectMaskList": "E7.2 PDN2", "LotToConfirmList": null, "LotToSplitList": "6AAED202M00", "PROJECTS": "DV41_LER DOE LER OPT LOT1", "LotToManageList": "5AAEL101M00,UGENT1 Proto"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201899025933306", "MaskList": "ABL", "ProjectMaskList": "SCTR1", "LotToConfirmList": null, "LotToSplitList": "6AAED201M00", "PROJECTS": "DV41 - STATUS LOT", "LotToManageList": "5AAEK101M00,femto-icomb run1"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201922419740872", "MaskList": "ABO", "ProjectMaskList": "Wolverine3", "LotToConfirmList": null, "LotToSplitList": "6AAAC202M00", "PROJECTS": "TW13 - RECIPE DOEs", "LotToManageList": "6AAEI201M00,FAU_WP1.9_RI_OXIDES"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1202129345004455", "MaskList": "ABP", "ProjectMaskList": "Wolverine3", "LotToConfirmList": null, "LotToSplitList": "6AAEB101M00", "PROJECTS": "LCP-01", "LotToManageList": "6AAAC203M00,METROLOGY_OPT DOE"}, {"requester_names_list": "Houssein EL DIRANI", "requester_gid_list": "1202135074352084", "MaskList": "ABR", "ProjectMaskList": "E6.6 MERLIN4", "LotToConfirmList": null, "LotToSplitList": "6AADZ101M00", "PROJECTS": "001204 BELLE", "LotToManageList": "6AAED202M00,DV41_LER DOE LER OPT LOT1"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202444549987515", "MaskList": "ABS", "ProjectMaskList": "E6.7 MERLIN5", "LotToConfirmList": null, "LotToSplitList": "6AADS101M00", "PROJECTS": "MPW-AN350-06", "LotToManageList": "6AAED201M00,DV41 - STATUS LOT"}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1202684057491700", "MaskList": "ABT", "ProjectMaskList": "E6.8", "LotToConfirmList": null, "LotToSplitList": "6AADQ101M00", "PROJECTS": "MPW-AN350-04", "LotToManageList": "6AAAC202M00,TW13 - RECIPE DOEs"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202907572860628", "MaskList": "ABU", "ProjectMaskList": "MPW-AN350-01B", "LotToConfirmList": null, "LotToSplitList": "6AADP101M00", "PROJECTS": "MPW-AN350-03", "LotToManageList": "6AAEB101M00,LCP-01"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203079565264253", "MaskList": "ABV", "ProjectMaskList": "1955_240501133", "LotToConfirmList": null, "LotToSplitList": "5AADO101M00", "PROJECTS": "MPW-AN800-40_2", "LotToManageList": "6AADZ101M00,001204 BELLE"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1203283724869310", "MaskList": "ABZ", "ProjectMaskList": "MPW-LN-01", "LotToConfirmList": null, "LotToSplitList": "5AADN101M00", "PROJECTS": "MPW-AN800-40", "LotToManageList": "6AADS101M00,MPW-AN350-06"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203601372370011", "MaskList": "ACA", "ProjectMaskList": "MPW-AN800-34", "LotToConfirmList": null, "LotToSplitList": "5AADM101M00", "PROJECTS": "MPW-AN800-39", "LotToManageList": "6AADQ101M00,MPW-AN350-04"}, {"requester_names_list": "Audie <PERSON>", "requester_gid_list": "1203873691444950", "MaskList": "ACB", "ProjectMaskList": "MAGNIFY-RUN 1", "LotToConfirmList": null, "LotToSplitList": "5AADL101M00", "PROJECTS": "MPW-AN800-37", "LotToManageList": "6AADP101M00,MPW-AN350-03"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1203869571959179", "MaskList": "ACF", "ProjectMaskList": "EWOC1", "LotToConfirmList": null, "LotToSplitList": "5AADK101M00", "PROJECTS": "MPW-AN800-38", "LotToManageList": "5AADO101M00,MPW-AN800-40_2"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204469536661025", "MaskList": "ACG", "ProjectMaskList": "DYNAMOS2", "LotToConfirmList": null, "LotToSplitList": "5AADJ101M00", "PROJECTS": "MPW-AN800-38_2", "LotToManageList": "5AADN101M00,MPW-AN800-40"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204562481875818", "MaskList": "ACH", "ProjectMaskList": "0586_240725270", "LotToConfirmList": null, "LotToSplitList": "5AADI101M00", "PROJECTS": "MPW-AN800-36", "LotToManageList": "5AADM101M00,MPW-AN800-39"}, {"requester_names_list": "Moustapha JAFFAL", "requester_gid_list": "1204562482451099", "MaskList": "ACL", "ProjectMaskList": "PF_WP8_LNOI", "LotToConfirmList": null, "LotToSplitList": "5AADC201M00", "PROJECTS": "MPW-LN-02", "LotToManageList": "5AADL101M00,MPW-AN800-37"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1204325331381742", "MaskList": "ACQ", "ProjectMaskList": "PF_WP7/8_PD_02", "LotToConfirmList": null, "LotToSplitList": "6AACZ102M00", "PROJECTS": "E2.23 HAD2 LOT2", "LotToManageList": "5AADK101M00,MPW-AN800-38"}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1204701988903544", "MaskList": "ACR", "ProjectMaskList": "PF_WP4_LOCA_S", "LotToConfirmList": null, "LotToSplitList": "6AACZ101M00", "PROJECTS": "E2.23 HAD2 LOT1", "LotToManageList": "5AADJ101M00,MPW-AN800-38_2"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204950886823380", "MaskList": "ACT", "ProjectMaskList": "EIG RIB DEV", "LotToConfirmList": null, "LotToSplitList": "6AACY101M00", "PROJECTS": "EIGER LSP4", "LotToManageList": "5AADI101M00,MPW-AN800-36"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204968822616977", "MaskList": "ACU", "ProjectMaskList": "MPW-AN800-34_2", "LotToConfirmList": null, "LotToSplitList": "6AACX101M00", "PROJECTS": "E2.15 Wolverine3 TRIM.", "LotToManageList": "5AADC201M00,MPW-LN-02"}, {"requester_names_list": "Viphretuo Mere", "requester_gid_list": "1204971721140670", "MaskList": "ACV", "ProjectMaskList": "SCTR1", "LotToConfirmList": null, "LotToSplitList": "6AACR201M00", "PROJECTS": "PF_WP4_LOCA_S", "LotToManageList": "6AACZ102M00,E2.23 HAD2 LOT2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205137449747223", "MaskList": "ACW", "ProjectMaskList": "ETV", "LotToConfirmList": null, "LotToSplitList": "5AACL201M17", "PROJECTS": "PF_WP8_LNOI", "LotToManageList": "6AACZ101M00,E2.23 HAD2 LOT1"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201278474361148", "MaskList": "ACX", "ProjectMaskList": "Wolverine3", "LotToConfirmList": null, "LotToSplitList": "6AABT201M09", "PROJECTS": "E6.8", "LotToManageList": "6AACY101M00,EIGER LSP4"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205595234938764", "MaskList": "ACY", "ProjectMaskList": "LSP4", "LotToConfirmList": null, "LotToSplitList": "6AABJ201M14", "PROJECTS": "E7.2 PDN2 LOT2", "LotToManageList": "6AACX101M00,E2.15 Wolverine3 TRIM."}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205709973073733", "MaskList": "ACZ", "ProjectMaskList": "HAD2", "LotToConfirmList": null, "LotToSplitList": "6AAAM204M00", "PROJECTS": "FAU_WP1.7.0_ILO_Plate_Height (LOT2)", "LotToManageList": "6AACR201M00,PF_WP4_LOCA_S"}, {"requester_names_list": "Yann K<PERSON>ZY<PERSON>", "requester_gid_list": "1204164213558726", "MaskList": "ADA", "ProjectMaskList": "photonixFAB_WP8_01", "LotToConfirmList": null, "LotToSplitList": "5AAEJ102M00", "PROJECTS": "MPW-AN800-35_2 RERUN", "LotToManageList": "5AACL201M17,PF_WP8_LNOI"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205847382524398", "MaskList": "ADC", "ProjectMaskList": "MPW-LN-02", "LotToConfirmList": null, "LotToSplitList": "6AAAM203M00", "PROJECTS": "FAU_WP1.7.0_ILO_Plate_Height", "LotToManageList": "6AABT201M09,E6.8"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1205874105867614", "MaskList": "ADD", "ProjectMaskList": "2nd Metal Layer - Shortloop", "LotToConfirmList": null, "LotToSplitList": "6AAEI202M00", "PROJECTS": "FAU_WP1.3.0/1/2_Electrostatic_Clamping_DOEs", "LotToManageList": "6AABJ201M14,E7.2 PDN2 LOT2"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1206225376712932", "MaskList": "ADE", "ProjectMaskList": "2235_29274", "LotToConfirmList": null, "LotToSplitList": "6AABS206M00", "PROJECTS": "E6.7 MERLIN5 RERUN3", "LotToManageList": "6AAAM204M00,FAU_WP1.7.0_ILO_Plate_Height (LOT2)"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206468476801011", "MaskList": "ADG", "ProjectMaskList": "MPW-AN800-35", "LotToConfirmList": null, "LotToSplitList": "6AABS205M00", "PROJECTS": "E6.7 MERLIN5 RERUN2", "LotToManageList": "5AAEJ102M00,MPW-AN800-35_2 RERUN"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477227688", "MaskList": "ADH", "ProjectMaskList": "MPW-AN350-02", "LotToConfirmList": null, "LotToSplitList": "6AACG101M13", "PROJECTS": "Dynamos2_an350", "LotToManageList": "6AAAM203M00,FAU_WP1.7.0_ILO_Plate_Height"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477262721", "MaskList": "ADI", "ProjectMaskList": "MPW-AN800-36", "LotToConfirmList": null, "LotToSplitList": "6AADH101M00", "PROJECTS": "MPW-AN350-02", "LotToManageList": "6AAEI202M00,FAU_WP1.3.0/1/2_Electrostatic_Clamping_DOEs"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206660149483972", "MaskList": "ADJ", "ProjectMaskList": "MPW-AN800-38_2", "LotToConfirmList": null, "LotToSplitList": "5AACQ202M00", "PROJECTS": "PF_WP7/8_PD_02", "LotToManageList": "6AABS206M00,E6.7 MERLIN5 RERUN3"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660407180541", "MaskList": "ADK", "ProjectMaskList": "MPW-AN800-38", "LotToConfirmList": null, "LotToSplitList": "5AACQ201M00", "PROJECTS": "PF_WP7/8_PD_02", "LotToManageList": "6AABS205M00,E6.7 MERLIN5 RERUN2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408294625", "MaskList": "ADL", "ProjectMaskList": "MPW-AN800-37", "LotToConfirmList": null, "LotToSplitList": "5AADU101M00", "PROJECTS": "QPIC Run 1", "LotToManageList": "6AACG101M13,Dynamos2_an350"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206660408413444", "MaskList": "ADM", "ProjectMaskList": "MPW-AN800-39", "LotToConfirmList": null, "LotToSplitList": "5AADG101M00", "PROJECTS": "MPW-AN800-35", "LotToManageList": "6AADH101M00,MPW-AN350-02"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408470048", "MaskList": "ADN", "ProjectMaskList": "MPW-AN800-40", "LotToConfirmList": null, "LotToSplitList": "6AADE101M00", "PROJECTS": "2235_29274", "LotToManageList": "5AACQ202M00,PF_WP7/8_PD_02"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408540230", "MaskList": "ADO", "ProjectMaskList": "MPW-AN800-40_2", "LotToConfirmList": null, "LotToSplitList": "6AACT201M00", "PROJECTS": "EIG RIB DEV", "LotToManageList": "5AACQ201M00,PF_WP7/8_PD_02"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207080700250290", "MaskList": "ADP", "ProjectMaskList": "MPW-AN350-03", "LotToConfirmList": null, "LotToSplitList": "5AADA201M00", "PROJECTS": "photonixFAB_WP8_01", "LotToManageList": "5AADU101M00,QPIC Run 1"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207080702899449", "MaskList": "ADQ", "ProjectMaskList": "MPW-AN350-04", "LotToConfirmList": null, "LotToSplitList": "6AABJ202M14", "PROJECTS": "E7.2 PDN2 LOT1", "LotToManageList": "5AADG101M00,MPW-AN800-35"}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1207245129912719", "MaskList": "ADS", "ProjectMaskList": "MPW-AN350-06", "LotToConfirmList": null, "LotToSplitList": "5AADI102M00", "PROJECTS": "MPW-AN800-36", "LotToManageList": "6AADE101M00,2235_29274"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1207368970469755", "MaskList": "ADU", "ProjectMaskList": "QPIC1", "LotToConfirmList": null, "LotToSplitList": "5AAER201M00", "PROJECTS": "PhotonixFAB_WP4.1_Lot1", "LotToManageList": "6AACT201M00,EIG RIB DEV"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207453515525652", "MaskList": "ADW", "ProjectMaskList": "TW13 qual", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AADA201M00,photonixFAB_WP8_01"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207686975432982", "MaskList": "ADX", "ProjectMaskList": "1037_2298", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AABJ202M14,E7.2 PDN2 LOT1"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1207823714901978", "MaskList": "ADZ", "ProjectMaskList": "001204 BELLE", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AADI102M00,MPW-AN800-36"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207389223844698", "MaskList": "AEB", "ProjectMaskList": "LCP-01", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AABJ201IL<PERSON>,E7.2 PDN2 LOT2"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207933125736626", "MaskList": "AED", "ProjectMaskList": "DV41_LER DOE LER OPT LOT1", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AABL203M00,E2.12 SCTR1.b rerun"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207933129699238", "MaskList": "AEI", "ProjectMaskList": "EIG_RI DOE OXIDES RI", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AACV201M00,E2.12 SCTR1.c LOT1"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207933129886224", "MaskList": "AEJ", "ProjectMaskList": "MPW-AN800-35_2", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AACV202M00,E2.12 SCTR1.c LOT2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1208432739984057", "MaskList": "AEK", "ProjectMaskList": "ICOMB1", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AABL202M00,E2.12 SCTR1.b"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1208438856853406", "MaskList": "AEL", "ProjectMaskList": "UGENT1", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "4AACB201M19,MAGNIFY-RUN 1"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1208438858900095", "MaskList": "AEN", "ProjectMaskList": "MPW-AN800-37", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AAER201M00,PhotonixFAB_WP4.1_Lot1"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1208650514227453", "MaskList": "AEO", "ProjectMaskList": "MPW-AN350-05", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1208866697419250", "MaskList": "AEQ", "ProjectMaskList": "NEW_HEATER", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1209025755733699", "MaskList": "AER", "ProjectMaskList": "PF", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1209138088509238", "MaskList": "AES", "ProjectMaskList": "LSP3X0", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1209235293455088", "MaskList": "AET", "ProjectMaskList": "TV7.1", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "Ines BELRHITI", "requester_gid_list": "1209299547695099", "MaskList": "AEU", "ProjectMaskList": "TNG3", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AEV", "ProjectMaskList": "TNG2", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AEW", "ProjectMaskList": "ETV6.4", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AEX", "ProjectMaskList": "CSOC3", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AEY", "ProjectMaskList": "JURA-LN1", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AEZ", "ProjectMaskList": "NGI_Circuit", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AFA", "ProjectMaskList": "NGI_Circuit", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AFB", "ProjectMaskList": "FOG_Spiral", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AFC", "ProjectMaskList": "luci", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": null, "requester_gid_list": null, "MaskList": "AFD", "ProjectMaskList": "ZERMATT-RUN3", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}], "capacity_data_frame": [{"Lot Type": "Prototype", "Number of Lots": 8.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN800", "Priority": "Low", "Production date in": "Monday", "FAB date in code": 0.0, "Projects": "MPW", "Task delay Update min (d)": 6.0, "Task advance update max (d)": 6.0, "max Lot reservation (d)": 14.0}, {"Lot Type": "Engineering", "Number of Lots": 8.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN350", "Priority": "Normal", "Production date in": "Tuesday", "FAB date in code": 1.0, "Projects": "EIGER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Pre-production", "Number of Lots": 4.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 4.0, "Wafer number max": 25, "Technology": "AN200", "Priority": "High", "Production date in": "Wednesday", "FAB date in code": 2.0, "Projects": "GLIDER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Proto-Engineering", "Number of Lots": 4.0, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": "25", "Technology": NaN, "Priority": NaN, "Production date in": "Thursday", "FAB date in code": 3.0, "Projects": "PHOTONIXFAB", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "PhotonixFAB", "Number of Lots": 10000.0, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": "25", "Technology": NaN, "Priority": NaN, "Production date in": "Friday", "FAB date in code": 4.0, "Projects": "MAGNIFY", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "DYNAMOS", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "QPIC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "FEMTOCHIP", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "Dedicated run AN800", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "<PERSON><PERSON>", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "PhotonHub", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "Dedicated run AN350", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "CSOC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": "                                                     ", "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "AN200 (engineering)", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "<PERSON><PERSON>", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "6G-EWOC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "FAB date in code": NaN, "Projects": "EU project", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}], "OP_data_frame": [{"MaskPlatform": "AN800", "DRC(d)": 14.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 3.0, "CS (d)": 10.0, "Pre-production[days]": 3.0, "Backend (d)": 14.0, "Split (d)": 3.0, "Drop off (d)": 2.0, "Tech_PCM": "AN800", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1YNjfsOnVtwb51LfxdSmlaYo3Al7R8t2YXl2doYmPgdg/edit?gid=772083255#gid=772083255", "Platform": "AN800", "Module name": "FF66223", "Names to be used in Talos selection": "FF66223 (classic frozen frame)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": "AN350", "DRC(d)": 14.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 14.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": "AN350", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1RHS_Gx6yG2D97pXFDUPWLo07vxudEYUvOXU3OuS-mSs/edit#gid=45736496", "Platform": "AN800", "Module name": "CORE", "Names to be used in Talos selection": "CORE (X1)", "New Mask Set[d]": 22, "Rerun[d]": 18, "Additional time [>10Wafers]": 5}, {"MaskPlatform": "AN200", "DRC(d)": 14.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": "AN200", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1tuzB1bX2zilyu5KX0brUwLKKFihsrsmrwU8qS3RlyMY/edit#gid=45736496", "Platform": "AN800", "Module name": "RIB", "Names to be used in Talos selection": "RIB", "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "X2", "Names to be used in Talos selection": "X2", "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "X3", "Names to be used in Talos selection": "X3", "New Mask Set[d]": 15, "Rerun[d]": 11, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if X2,X3 selected)", "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if X2,X3 selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223", "Names to be used in Talos selection": "FF66223 (classic frozen frame)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN350", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66643", "Names to be used in Talos selection": "FF66643 (small frame EIGER only)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223-1mm", "Names to be used in Talos selection": "FF66223-1mm  (new reduced frame EIGER only)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "RESIST_OPEN", "Names to be used in Talos selection": "RESIST_OPEN", "New Mask Set[d]": 5, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3", "Names to be used in Talos selection": "MX3 (core)", "New Mask Set[d]": 18, "Rerun[d]": 15, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "6G-EWOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": "        ", "Platform": "AN350", "Module name": "MX3RIB", "Names to be used in Talos selection": "MX3RIB (rib)", "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "EU project", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3X2", "Names to be used in Talos selection": "MX3X2", "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "BOWPOLYSI", "Names to be used in Talos selection": "BOW+POLY-SI (Backside bow compensation layer + P-Si layer for clamping)", "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if MX3X2 selected)", "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if MX3X2 selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA-S", "Names to be used in Talos selection": "LOCA-S ", "New Mask Set[d]": 20, "Rerun[d]": 16, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "MX2", "Names to be used in Talos selection": "MX2 (core)", "New Mask Set[d]": 16, "Rerun[d]": 12, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "ISOTRENCH", "Names to be used in Talos selection": "ISOTRENCH", "New Mask Set[d]": 7, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}], "ENG_data_frame": [{"MaskPlatform": "AN800", "DRC(d)": 14.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 3.0, "CS (d)": 10.0, "Pre-production[days]": 3.0, "Backend (d)": 14.0, "Split (d)": 3.0, "Drop off (d)": 2.0, "Tech_PCM": "AN800", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1YNjfsOnVtwb51LfxdSmlaYo3Al7R8t2YXl2doYmPgdg/edit?gid=772083255#gid=772083255", "Platform": "AN800", "Module name": "FF66223", "Names to be used in Talos selection": "FF66223 (classic frozen frame)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": "AN350", "DRC(d)": 14.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 14.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": "AN350", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1RHS_Gx6yG2D97pXFDUPWLo07vxudEYUvOXU3OuS-mSs/edit#gid=45736496", "Platform": "AN800", "Module name": "CORE", "Names to be used in Talos selection": "CORE (X1)", "New Mask Set[d]": 22, "Rerun[d]": 18, "Additional time [>10Wafers]": 5}, {"MaskPlatform": "AN200", "DRC(d)": 14.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": "AN200", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1tuzB1bX2zilyu5KX0brUwLKKFihsrsmrwU8qS3RlyMY/edit#gid=45736496", "Platform": "AN800", "Module name": "RIB", "Names to be used in Talos selection": "RIB", "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "X2", "Names to be used in Talos selection": "X2", "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "X3", "Names to be used in Talos selection": "X3", "New Mask Set[d]": 15, "Rerun[d]": 11, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if X2,X3 selected)", "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if X2,X3 selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223", "Names to be used in Talos selection": "FF66223 (classic frozen frame)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN350", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66643", "Names to be used in Talos selection": "FF66643 (small frame EIGER only)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223-1mm", "Names to be used in Talos selection": "FF66223-1mm  (new reduced frame EIGER only)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "RESIST_OPEN", "Names to be used in Talos selection": "RESIST_OPEN", "New Mask Set[d]": 5, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3", "Names to be used in Talos selection": "MX3 (core)", "New Mask Set[d]": 18, "Rerun[d]": 15, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "6G-EWOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3RIB", "Names to be used in Talos selection": "MX3RIB (rib)", "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "EU project", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3X2", "Names to be used in Talos selection": "MX3X2", "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "BOWPOLYSI", "Names to be used in Talos selection": "BOW+POLY-SI (Backside bow compensation layer + P-Si layer for clamping)", "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if MX3X2 selected)", "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if MX3X2 selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA-S", "Names to be used in Talos selection": "LOCA-S ", "New Mask Set[d]": 20, "Rerun[d]": 16, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "MX2", "Names to be used in Talos selection": "MX2 (core)", "New Mask Set[d]": 16, "Rerun[d]": 12, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "DRC(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Drop off (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "ISOTRENCH", "Names to be used in Talos selection": "ISOTRENCH", "New Mask Set[d]": 7, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}], "AsanaProject_data_frame": [{"Asana project": "Mask assembly", "project gid": 1203927479554343, "Portfolio": "Prototype", "Portfolio gid": 1206397258164834.0, "Talos version": "1.0.0"}, {"Asana project": "RFQ-PO", "project gid": 1206397258493014, "Portfolio": "Engineering", "Portfolio gid": 1206397258164837.0, "Talos version": NaN}, {"Asana project": "PI", "project gid": 1206397258492997, "Portfolio": "PhotonixFAB", "Portfolio gid": 1207525923277412.0, "Talos version": NaN}, {"Asana project": "Testing", "project gid": 1206397258164847, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Data processing", "project gid": 1206397258493001, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Shipment", "project gid": 1206397258493005, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "CS", "project gid": 1206521529181046, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Backend", "project gid": 1206110145947517, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "TestingCH", "project gid": 1202032814431764, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Grinding", "project gid": 1206521529181050, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "QAGate", "project gid": 1205868885496972, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": ","}], "LotNumber_data_frame": [{"Technology": "AN800", "Technology code": "5A", "Lot Type": "Pre-production", "Lot Type code": 0.0, "Modules": "X1+Heater", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "CORE", "Layer code": "X1B", "Layer Name": "X1 bonding", "Unnamed: 12": NaN, "Technology (2digits)": "LGT code", "Unnamed: 14": "Technology", "Mask Number (3 digits)": "LGT Code ", "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": "XFAB Mask ref", "Lot type (1 digit)": "LGT code", "Unnamed: 20": "Lot type", "Run Number (2 digits)": "LGT Code ", "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": "LGT code", "Unnamed: 25": NaN, "Unnamed: 26": "Process step"}, {"Technology": "AN350", "Technology code": "6A", "Lot Type": "Prototype", "Lot Type code": 1.0, "Modules": "X1+Heater", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "X2", "Layer code": "X2B", "Layer Name": "X2 bonding", "Unnamed: 12": NaN, "Technology (2digits)": "5A", "Unnamed: 14": "AN800", "Mask Number (3 digits)": "A", "Unnamed: 16": "A", "Unnamed: 17": "A", "Unnamed: 18": NaN, "Lot type (1 digit)": 0, "Unnamed: 20": "Pre-production", "Run Number (2 digits)": 0, "Unnamed: 22": 0.0, "Unnamed: 23": 0.0, "Process step (2digits)": 0, "Unnamed: 25": 0.0, "Unnamed: 26": "End"}, {"Technology": "AN200", "Technology code": "4A", "Lot Type": "Engineering", "Lot Type code": 2.0, "Modules": "X1+ILDB", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "X3 ", "Layer code": "X3B", "Layer Name": "X3 bonding", "Unnamed: 12": NaN, "Technology (2digits)": "6A", "Unnamed: 14": "AN350", "Mask Number (3 digits)": 0, "Unnamed: 16": 0, "Unnamed: 17": 1, "Unnamed: 18": NaN, "Lot type (1 digit)": 1, "Unnamed: 20": "Prototype", "Run Number (2 digits)": 0, "Unnamed: 22": 0.0, "Unnamed: 23": 1.0, "Process step (2digits)": 0, "Unnamed: 25": 1.0, "Unnamed: 26": "X1"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": "Production", "Lot Type code": 3.0, "Modules": "X1+ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "ILDA", "Layer code": "ILA", "Layer Name": "ILDA", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": 0, "Unnamed: 16": 0, "Unnamed: 17": 2, "Unnamed: 18": NaN, "Lot type (1 digit)": 2, "Unnamed: 20": "Engineering", "Run Number (2 digits)": 0, "Unnamed: 22": 0.0, "Unnamed: 23": 2.0, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": "X2"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "ILDB", "Layer code": "ILB", "Layer Name": "ILDB", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": 3, "Unnamed: 20": "Production", "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": "HEATER"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "HEATER", "Layer code": "HET", "Layer Name": "HEATER", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X2 bonding", "Layer_code": "X2B", "Layer state": 100, "Platform": "AN800", "Module name": "LOCA", "Layer code": "LCI", "Layer Name": "LOCA-i", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "RIB", "Layer code": "RIB", "Layer Name": "RIB bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "UCUT", "Layer code": "UCT", "Layer Name": "UCUT", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "HEATER", "Layer_code": "HET", "Layer state": 100, "Platform": "AN350", "Module name": "MX3", "Layer code": "MX3", "Layer Name": "MX3 bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN350", "Module name": "MX3X2", "Layer code": "X2B", "Layer Name": "MX3X2 bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": "Example", "Mask Number (3 digits)": "05-044-1-00-2", "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN350", "Module name": "BOWPOLYSI", "Layer code": "BPS", "Layer Name": "BOWPOLYSI", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "X1 RIB bonding", "Layer_code": "1RB", "Layer state": 100, "Platform": "AN350", "Module name": "ILDA", "Layer code": "ILA", "Layer Name": "ILDA", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN350", "Module name": "ILDB", "Layer code": "ILB", "Layer Name": "ILDB", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN350", "Module name": "HEATER", "Layer code": "HET", "Layer Name": "HEATER", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN350", "Module name": "MX3RIB", "Layer code": "RIB", "Layer Name": "RIB bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN350", "Module name": "LOCA", "Layer code": "LCI", "Layer Name": "LOCA-i", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN350", "Module name": "LOCA-S", "Layer code": "LCS", "Layer Name": "LOCA-s", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN350", "Module name": "UCUT", "Layer code": "UCT", "Layer Name": "UCUT", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN200", "Module name": "MX2", "Layer code": "MX2", "Layer Name": "MX2 bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "X3 RIB bonding", "Layer_code": "3RB", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "X3 RIB bonding", "Layer_code": "3RB", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "X3 RIB bonding", "Layer_code": "3RB", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+Heater", "Layer": "END", "Layer_code": "M00", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+ILDB", "Layer": "END", "Layer_code": "M01", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "END", "Layer_code": "M02", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "END", "Layer_code": "M03", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3 bonding", "Layer": "END", "Layer_code": "M04", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "END", "Layer_code": "M05", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "END", "Layer_code": "M06", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "END", "Layer_code": "M07", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 Bonding ", "Layer": "END", "Layer_code": "M08", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "END", "Layer_code": "M09", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "END", "Layer_code": "M10", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "END", "Layer_code": "M11", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "END", "Layer_code": "M12", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "END", "Layer_code": "M13", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "END", "Layer_code": "M14", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater+LOCA-i", "Layer": "END", "Layer_code": "M15", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater", "Layer": "END", "Layer_code": "M16", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1 bonding", "Layer": "END", "Layer_code": "M17", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "SiN dep", "Layer": "END", "Layer_code": "M18", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX2 Bonding", "Layer": "END", "Layer_code": "M19", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+LOCA-i", "Layer": "END", "Layer_code": "M20", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X3+X2+LOCA-S", "Layer": "END", "Layer_code": "M21", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}], "Email_frame": [{"Requester": "<PERSON><PERSON>", "Acronym": "dav"}, {"Requester": "<PERSON>", "Acronym": "maz"}, {"Requester": "<PERSON>", "Acronym": "mwg"}, {"Requester": "<PERSON><PERSON>", "Acronym": "gbn"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "kal"}, {"Requester": "<PERSON>", "Acronym": "tat"}, {"Requester": "<PERSON>", "Acronym": "ast"}, {"Requester": "<PERSON><PERSON>", "Acronym": "fin"}, {"Requester": "<PERSON>", "Acronym": "tom"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "arc"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mar"}, {"Requester": "Ana Catarina <PERSON>", "Acronym": "acm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "ozp"}, {"Requester": "<PERSON>", "Acronym": "hry"}, {"Requester": "<PERSON>", "Acronym": "anb"}, {"Requester": "<PERSON>", "Acronym": "anf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "jeg"}, {"Requester": "<PERSON>", "Acronym": "jfc"}, {"Requester": "<PERSON> Yao", "Acronym": "cay"}, {"Requester": "<PERSON>", "Acronym": "piw"}, {"Requester": "<PERSON><PERSON>", "Acronym": "cob"}, {"Requester": "<PERSON>", "Acronym": "jdf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "plz"}, {"Requester": "<PERSON>", "Acronym": "sab"}, {"Requester": "<PERSON>", "Acronym": "stm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "asa"}, {"Requester": "<PERSON>", "Acronym": "axp"}, {"Requester": "<PERSON>", "Acronym": "gug"}, {"Requester": "Houssein EL DIRANI", "Acronym": "hod"}, {"Requester": "<PERSON><PERSON>", "Acronym": "dab"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON>", "Acronym": "abh"}, {"Requester": "<PERSON><PERSON>", "Acronym": "lde"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "alf"}, {"Requester": "<PERSON>", "Acronym": "cls"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "alr"}, {"Requester": "Audie <PERSON>", "Acronym": "aye"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mat"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "stc"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "git"}, {"Requester": "Moustapha JAFFAL", "Acronym": "moj"}, {"Requester": "<PERSON>", "Acronym": "anv"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Acronym": "abr"}, {"Requester": "Ulysse Fanda", "Acronym": "ulf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "chr"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "khk"}, {"Requester": "Viphretuo Mere", "Acronym": "vim"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "zba"}, {"Requester": "<PERSON>", "Acronym": "fov"}, {"Requester": "<PERSON><PERSON>", "Acronym": "jod"}, {"Requester": "<PERSON>", "Acronym": "frg"}, {"Requester": "<PERSON>", "Acronym": "joj"}, {"Requester": "Yann K<PERSON>ZY<PERSON>", "Acronym": "yak"}, {"Requester": "<PERSON>", "Acronym": "gab"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "swp"}, {"Requester": "<PERSON><PERSON>", "Acronym": "yil"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "sds"}, {"Requester": "<PERSON>", "Acronym": "mab"}, {"Requester": "<PERSON>", "Acronym": "ann"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "myl"}, {"Requester": "<PERSON>", "Acronym": "boz"}, {"Requester": "<PERSON>", "Acronym": "alb"}, {"Requester": "<EMAIL>", "Acronym": "adi"}, {"Requester": "<PERSON>", "Acronym": "lec"}, {"Requester": "<PERSON>", "Acronym": "dib"}, {"Requester": "<PERSON>", "Acronym": "emg"}], "project_data_frame": [{"list_projects_names": "6AAAN101M14,EIGER 2.16, start : 2024-05-15 , 232 days", "list_Start_ON": "2024-05-15", "list_Due_ON": "2025-01-02", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "232", "RFQPOin": "2024-05-27", "RFQPOout": "2024-06-06", "ERFPOin": "2024-11-11", "ERFPOout": "2024-11-20", "TOin": "2024-05-15", "TOout": "2024-06-14", "PIin": "2024-07-16", "PIout": "2024-11-19", "PrePin": "", "PrePout": "", "Testout": "2024-11-20", "DataProcessOut": "2024-11-20", "ShipmentOut": "2024-11-25", "CSout": "2024-12-20", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "10", "list_title": "6AAAN101M14,<PERSON>IG<PERSON> 2.16", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "5AABV101M01,1955_240501133, start : 2024-07-05 , 224 days", "list_Start_ON": "2024-07-05", "list_Due_ON": "2025-02-14", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "224", "RFQPOin": "2024-06-27", "RFQPOout": "2024-07-08", "ERFPOin": "2024-09-23", "ERFPOout": "2024-10-04", "TOin": "2024-07-05", "TOout": "2024-07-23", "PIin": "2024-07-17", "PIout": "2024-09-30", "PrePin": "", "PrePout": "", "Testout": "2024-10-02", "DataProcessOut": "2024-10-02", "ShipmentOut": "2024-10-05", "CSout": "2024-11-05", "BackendOut": "2024-11-15", "list_requester": "<PERSON> Yao", "WaferNumberList": "6", "list_title": "5AABV101M01,1955_240501133", "list_modules": "Proto lot : FF66223 , CORE , ILDB , CS", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AACA101M03,MPW-AN800-34, start : 2024-08-12 , 181 days", "list_Start_ON": "2024-08-12", "list_Due_ON": "2025-02-09", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "181", "RFQPOin": "2024-08-12", "RFQPOout": "2024-08-22", "ERFPOin": "2024-11-23", "ERFPOout": "2024-12-03", "TOin": "2024-08-12", "TOout": "2024-08-30", "PIin": "2024-08-29", "PIout": "2024-12-13", "PrePin": "", "PrePout": "", "Testout": "2024-12-15", "DataProcessOut": "2024-12-16", "ShipmentOut": "2024-12-20", "CSout": "2025-01-29", "BackendOut": "2025-02-09", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AACA101M03,MPW-AN800-34", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "6AACG101M13,Dynamos2_an350, start : 2024-11-12 , 125 days", "list_Start_ON": "2024-11-12", "list_Due_ON": "2025-03-17", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "125", "RFQPOin": "2024-11-14", "RFQPOout": "2024-11-22", "ERFPOin": "2025-02-08", "ERFPOout": "2025-02-18", "TOin": "2024-11-12", "TOout": "2024-12-09", "PIin": "2025-01-09", "PIout": "2025-02-17", "PrePin": "", "PrePout": "", "Testout": "2025-02-18", "DataProcessOut": "2025-02-19", "ShipmentOut": "2025-02-21", "CSout": "2025-03-03", "BackendOut": "2025-03-17", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "6AACG101M13,Dynamos2_an350", "list_modules": "Proto lot : FF66223 , MX3 , ILDB , MX3RIB , CS", "list_project": "DYNAMOS"}, {"list_projects_names": "5AACH101M03,0586_240725270, start : 2024-08-30 , 182 days", "list_Start_ON": "2024-08-30", "list_Due_ON": "2025-02-28", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "182", "RFQPOin": "2024-08-30", "RFQPOout": "2024-09-23", "ERFPOin": "2025-01-28", "ERFPOout": "2025-02-07", "TOin": "2024-08-30", "TOout": "2024-09-17", "PIin": "2024-10-03", "PIout": "2025-01-09", "PrePin": "", "PrePout": "", "Testout": "2025-01-10", "DataProcessOut": "2025-01-10", "ShipmentOut": "2025-02-10", "CSout": "2025-02-20", "BackendOut": "2025-02-28", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AACH101M03,0586_240725270", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AACA102M00,MPW-AN800-34,Splitted lot, start : 2024-08-12 , 181 days", "list_Start_ON": "2024-08-12", "list_Due_ON": "2025-02-09", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "181", "RFQPOin": "2024-08-12", "RFQPOout": "2024-08-22", "ERFPOin": "2024-11-08", "ERFPOout": "2024-11-18", "TOin": "2024-08-12", "TOout": "2024-08-30", "PIin": "2024-10-15", "PIout": "2024-11-13", "PrePin": "", "PrePout": "", "Testout": "2024-11-13", "DataProcessOut": "2024-11-14", "ShipmentOut": "2024-11-19", "CSout": "2025-01-29", "BackendOut": "2025-02-09", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AACA102M00,MPW-AN800-34,Splitted lot", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDB , CS", "list_project": "MPW"}, {"list_projects_names": "5AADI101M00,MPW-AN800-36, start : 2025-01-23 , 130 days", "list_Start_ON": "2025-01-23", "list_Due_ON": "2025-06-02", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "130", "RFQPOin": "2025-02-06", "RFQPOout": "2025-02-16", "ERFPOin": "2025-04-26", "ERFPOout": "2025-05-06", "TOin": "2025-01-23", "TOout": "2025-02-24", "PIin": "2025-02-24", "PIout": "2025-05-05", "PrePin": "", "PrePout": "", "Testout": "2025-05-06", "DataProcessOut": "2025-05-07", "ShipmentOut": "2025-05-09", "CSout": "2025-05-19", "BackendOut": "2025-06-02", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "3", "list_title": "5AADI101M00,MPW-AN800-36", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "5AADJ101M00,MPW-AN800-38_2, start : 2025-06-05 , 91 days", "list_Start_ON": "2025-06-05", "list_Due_ON": "2025-09-04", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "91", "RFQPOin": "2025-06-05", "RFQPOout": "2025-06-15", "ERFPOin": "2025-07-29", "ERFPOout": "2025-08-08", "TOin": "2025-06-05", "TOout": "2025-06-23", "PIin": "2025-06-23", "PIout": "2025-08-06", "PrePin": "", "PrePout": "", "Testout": "2025-08-07", "DataProcessOut": "2025-08-08", "ShipmentOut": "2025-08-11", "CSout": "2025-08-21", "BackendOut": "2025-09-04", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AADJ101M00,MPW-AN800-38_2", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDB , CS", "list_project": "MPW"}, {"list_projects_names": "5AADK101M00,MPW-AN800-38, start : 2025-06-05 , 127 days", "list_Start_ON": "2025-06-05", "list_Due_ON": "2025-10-10", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "127", "RFQPOin": "2025-06-05", "RFQPOout": "2025-06-15", "ERFPOin": "2025-09-03", "ERFPOout": "2025-09-13", "TOin": "2025-06-05", "TOout": "2025-06-23", "PIin": "2025-06-23", "PIout": "2025-09-11", "PrePin": "", "PrePout": "", "Testout": "2025-09-12", "DataProcessOut": "2025-09-13", "ShipmentOut": "2025-09-16", "CSout": "2025-09-26", "BackendOut": "2025-10-10", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AADK101M00,MPW-AN800-38", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "5AADM101M00,MPW-AN800-39, start : 2025-08-04 , 117 days", "list_Start_ON": "2025-08-04", "list_Due_ON": "2025-11-29", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "117", "RFQPOin": "2025-08-04", "RFQPOout": "2025-08-14", "ERFPOin": "2025-10-23", "ERFPOout": "2025-11-02", "TOin": "2025-08-04", "TOout": "2025-08-22", "PIin": "2025-08-22", "PIout": "2025-10-31", "PrePin": "", "PrePout": "", "Testout": "2025-11-01", "DataProcessOut": "2025-11-02", "ShipmentOut": "2025-11-05", "CSout": "2025-11-15", "BackendOut": "2025-11-29", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AADM101M00,MPW-AN800-39", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "5AADN101M00,MPW-AN800-40, start : 2025-11-06 , 127 days", "list_Start_ON": "2025-11-06", "list_Due_ON": "2026-03-13", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "127", "RFQPOin": "2025-11-06", "RFQPOout": "2025-11-16", "ERFPOin": "2026-02-04", "ERFPOout": "2026-02-14", "TOin": "2025-11-06", "TOout": "2025-11-24", "PIin": "2025-11-24", "PIout": "2026-02-12", "PrePin": "", "PrePout": "", "Testout": "2026-02-13", "DataProcessOut": "2026-02-14", "ShipmentOut": "2026-02-17", "CSout": "2026-02-27", "BackendOut": "2026-03-13", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AADN101M00,MPW-AN800-40", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "5AADO101M00,MPW-AN800-40_2, start : 2025-11-06 , 91 days", "list_Start_ON": "2025-11-06", "list_Due_ON": "2026-02-05", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "91", "RFQPOin": "2025-11-06", "RFQPOout": "2025-11-16", "ERFPOin": "2025-12-30", "ERFPOout": "2026-01-09", "TOin": "2025-11-06", "TOout": "2025-11-24", "PIin": "2025-11-24", "PIout": "2026-01-07", "PrePin": "", "PrePout": "", "Testout": "2026-01-08", "DataProcessOut": "2026-01-09", "ShipmentOut": "2026-01-12", "CSout": "2026-01-22", "BackendOut": "2026-02-05", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AADO101M00,MPW-AN800-40_2", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDB , CS", "list_project": "MPW"}, {"list_projects_names": "6AADP101M00,MPW-AN350-03, start : 2025-03-06 , 110 days", "list_Start_ON": "2025-03-06", "list_Due_ON": "2025-06-24", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "110", "RFQPOin": "2025-03-06", "RFQPOout": "2025-03-16", "ERFPOin": "2025-05-18", "ERFPOout": "2025-05-28", "TOin": "2025-03-06", "TOout": "2025-03-31", "PIin": "2025-03-31", "PIout": "2025-05-26", "PrePin": "", "PrePout": "", "Testout": "2025-05-27", "DataProcessOut": "2025-05-28", "ShipmentOut": "2025-05-31", "CSout": "2025-06-10", "BackendOut": "2025-06-24", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AADP101M00,MPW-AN350-03", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "6AADQ101M00,MPW-AN350-04, start : 2025-05-05 , 108 days", "list_Start_ON": "2025-05-05", "list_Due_ON": "2025-08-21", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "108", "RFQPOin": "2025-05-05", "RFQPOout": "2025-05-15", "ERFPOin": "2025-07-15", "ERFPOout": "2025-07-25", "TOin": "2025-05-05", "TOout": "2025-05-30", "PIin": "2025-05-30", "PIout": "2025-07-23", "PrePin": "", "PrePout": "", "Testout": "2025-07-24", "DataProcessOut": "2025-07-25", "ShipmentOut": "2025-07-28", "CSout": "2025-08-07", "BackendOut": "2025-08-21", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AADQ101M00,MPW-AN350-04", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "6AADS101M00,MPW-AN350-06, start : 2025-11-06 , 110 days", "list_Start_ON": "2025-11-06", "list_Due_ON": "2026-02-24", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "110", "RFQPOin": "2025-11-06", "RFQPOout": "2025-11-16", "ERFPOin": "2026-01-18", "ERFPOout": "2026-01-28", "TOin": "2025-11-06", "TOout": "2025-12-01", "PIin": "2025-12-01", "PIout": "2026-01-26", "PrePin": "", "PrePout": "", "Testout": "2026-01-27", "DataProcessOut": "2026-01-28", "ShipmentOut": "2026-01-31", "CSout": "2026-02-10", "BackendOut": "2026-02-24", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AADS101M00,MPW-AN350-06", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "6AAAM201MX3,E2.14,MX3 bonding, start : 2024-05-24 , 264 days", "list_Start_ON": "2024-05-24", "list_Due_ON": "2025-02-12", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "264", "RFQPOin": "2024-07-04", "RFQPOout": "2024-07-08", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-24", "TOout": "2024-07-31", "PIin": "2024-11-21", "PIout": "2024-11-25", "PrePin": "", "PrePout": "", "Testout": "2025-01-22", "DataProcessOut": "", "ShipmentOut": "2025-02-12", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AAAM201MX3,E2.14,MX3 bonding", "list_modules": "Proto lot : FF66223-1mm , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AABJ201ILA,E7.2 PDN2 LOT2,ILDA, start : 2024-10-03 , 159 days", "list_Start_ON": "2024-10-03", "list_Due_ON": "2025-03-11", "list_corridor": "Inactive (Proto)", "list_tech": "AN350", "list_cycleTime": "159", "RFQPOin": "2024-10-03", "RFQPOout": "2024-10-23", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-10-03", "TOout": "2024-10-28", "PIin": "2025-03-10", "PIout": "2025-03-11", "PrePin": "", "PrePout": "", "Testout": "2025-01-07", "DataProcessOut": "2025-01-08", "ShipmentOut": "2025-01-10", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "2", "list_title": "6AABJ201IL<PERSON>,E7.2 PDN2 LOT2,ILDA", "list_modules": "Proto lot : FF66223, MX3, MX3RIB, ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AADX101M00,1037_2298, start : 2024-10-23 , 96 days", "list_Start_ON": "2024-10-23", "list_Due_ON": "2025-01-27", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "96", "RFQPOin": "2024-10-28", "RFQPOout": "2024-11-08", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-10-23", "TOout": "2024-11-18", "PIin": "2024-12-03", "PIout": "2025-01-21", "PrePin": "", "PrePout": "", "Testout": "2025-01-24", "DataProcessOut": "2025-01-24", "ShipmentOut": "2025-01-27", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "14", "list_title": "6AADX101M00,1037_2298", "list_modules": "Proto lot : FF66223 , MX3 , ILDA", "list_project": "Dedicated run AN350"}, {"list_projects_names": "6AABJ202ILA,E7.2 PDN2 LOT1,ILDA, start : 2025-01-28 , 3 days", "list_Start_ON": "2025-01-28", "list_Due_ON": "2025-01-31", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "3", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-28", "PIout": "2025-01-28", "PrePin": "", "PrePout": "", "Testout": "2025-01-29", "DataProcessOut": "2025-01-30", "ShipmentOut": "2025-01-31", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "3", "list_title": "6AABJ202<PERSON><PERSON>,E7.2 PDN2 LOT1,ILDA", "list_modules": "Proto lot : FF66223, MX3, MX3RIB, ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABS104M00,E6.7 MERLIN5 RERUN1, start : 2024-12-12 , 49 days", "list_Start_ON": "2024-12-12", "list_Due_ON": "2025-01-30", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "49", "RFQPOin": "2024-11-04", "RFQPOout": "2024-11-13", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-12-12", "PIout": "2025-01-20", "PrePin": "", "PrePout": "", "Testout": "2025-01-21", "DataProcessOut": "2025-01-21", "ShipmentOut": "2025-01-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AABS104M00,E6.7 MERLIN5 RERUN1", "list_modules": "Proto lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABR102M00,MERLIN4 - E6.6 RERUN, start : 2024-12-02 , 59 days", "list_Start_ON": "2024-12-02", "list_Due_ON": "2025-01-30", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "59", "RFQPOin": "2024-11-04", "RFQPOout": "2024-11-14", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-12-02", "PIout": "2025-01-16", "PrePin": "", "PrePout": "", "Testout": "2025-01-17", "DataProcessOut": "2025-01-17", "ShipmentOut": "2025-01-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AABR102M00,MERLIN4 - E6.6 RERUN", "list_modules": "Proto lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "5AAEJ101M00,MPW-AN800-35_2, start : 2024-11-17 , 106 days", "list_Start_ON": "2024-11-17", "list_Due_ON": "2025-03-03", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "106", "RFQPOin": "2024-11-17", "RFQPOout": "2024-11-22", "ERFPOin": "2025-01-25", "ERFPOout": "2025-02-04", "TOin": "2024-11-17", "TOout": "2024-12-05", "PIin": "2024-11-28", "PIout": "2025-02-03", "PrePin": "", "PrePout": "", "Testout": "2025-02-04", "DataProcessOut": "2025-02-05", "ShipmentOut": "2025-02-07", "CSout": "2025-02-17", "BackendOut": "2025-03-03", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AAEJ101M00,MPW-AN800-35_2", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDB , CS", "list_project": "MPW"}, {"list_projects_names": "5AAEK101M00,femto-icomb run1, start : 2025-01-21 , 85 days", "list_Start_ON": "2025-01-21", "list_Due_ON": "2025-04-16", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "85", "RFQPOin": "2025-02-06", "RFQPOout": "2025-02-16", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-01-21", "TOout": "2025-02-24", "PIin": "2025-03-10", "PIout": "2025-04-16", "PrePin": "", "PrePout": "", "Testout": "2025-04-10", "DataProcessOut": "2025-04-11", "ShipmentOut": "2025-04-14", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AAEK101M00,femto-icomb run1", "list_modules": "Proto lot : FF66223 , <PERSON><PERSON> , X3", "list_project": "FEMTOCHIP"}, {"list_projects_names": "6AABG103M00,CH5A - RERUN2, start : 2024-11-14 , 56 days", "list_Start_ON": "2024-11-14", "list_Due_ON": "2025-01-09", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "56", "RFQPOin": "2024-12-30", "RFQPOout": "2025-01-09", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-11-14", "PIout": "2024-12-03", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-12-06", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABG103M00,CH5A - RERUN2", "list_modules": "Proto lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AAAN103M00,E2.16 - HAD1 - R<PERSON><PERSON>, start : 2024-11-21 , 89 days", "list_Start_ON": "2024-11-21", "list_Due_ON": "2025-02-18", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "89", "RFQPOin": "2024-11-24", "RFQPOout": "2024-12-04", "ERFPOin": "2025-01-26", "ERFPOout": "2025-02-05", "TOin": "", "TOout": "", "PIin": "2024-11-21", "PIout": "2025-01-29", "PrePin": "", "PrePout": "", "Testout": "2025-02-03", "DataProcessOut": "2025-02-03", "ShipmentOut": "2025-02-05", "CSout": "2025-02-18", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "12", "list_title": "6AAAN103M00,E2.16 - HAD1 - <PERSON><PERSON><PERSON>", "list_modules": "Proto lot : FF66223 , MX3 , MX3RIB , ILDA , HEATER , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AAEO101M00,MPW-AN350-05, start : 2025-07-04 , 123 days", "list_Start_ON": "2025-07-04", "list_Due_ON": "2025-11-04", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "123", "RFQPOin": "2025-07-18", "RFQPOout": "2025-07-28", "ERFPOin": "2025-09-28", "ERFPOout": "2025-10-08", "TOin": "2025-07-04", "TOout": "2025-08-12", "PIin": "2025-08-12", "PIout": "2025-10-06", "PrePin": "", "PrePout": "", "Testout": "2025-10-07", "DataProcessOut": "2025-10-08", "ShipmentOut": "2025-10-11", "CSout": "2025-10-21", "BackendOut": "2025-11-04", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AAEO101M00,MPW-AN350-05", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "6AAEV101M00,TNG2, start : 2025-03-01 , 89 days", "list_Start_ON": "2025-03-01", "list_Due_ON": "2025-05-29", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "89", "RFQPOin": "2025-03-15", "RFQPOout": "2025-03-25", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-03-01", "TOout": "2025-04-09", "PIin": "2025-04-09", "PIout": "2025-05-22", "PrePin": "", "PrePout": "", "Testout": "2025-05-25", "DataProcessOut": "2025-05-26", "ShipmentOut": "2025-05-29", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AAEV101M00,TNG2", "list_modules": "Proto lot : FF66223-1mm , RESIST_OPEN , MX3 , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "6AAEW101M00,ETV6.4, start : 2025-03-01 , 89 days", "list_Start_ON": "2025-03-01", "list_Due_ON": "2025-05-29", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "89", "RFQPOin": "2025-03-15", "RFQPOout": "2025-03-25", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-03-01", "TOout": "2025-04-09", "PIin": "2025-04-09", "PIout": "2025-05-22", "PrePin": "", "PrePout": "", "Testout": "2025-05-25", "DataProcessOut": "2025-05-26", "ShipmentOut": "2025-05-29", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AAEW101M00,ETV6.4", "list_modules": "Proto lot : FF66223-1mm , RESIST_OPEN , MX3 , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "5AADI102M00,MPW-AN800-36,Splitted lot, start : 2025-01-23 , 151 days", "list_Start_ON": "2025-01-23", "list_Due_ON": "2025-06-23", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "151", "RFQPOin": "2025-02-06", "RFQPOout": "2025-02-16", "ERFPOin": "2025-05-17", "ERFPOout": "2025-05-27", "TOin": "2025-01-23", "TOout": "2025-02-24", "PIin": "2025-03-31", "PIout": "2025-05-26", "PrePin": "", "PrePout": "", "Testout": "2025-05-27", "DataProcessOut": "2025-05-28", "ShipmentOut": "2025-05-30", "CSout": "2025-06-09", "BackendOut": "2025-06-23", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "3", "list_title": "5AADI102M00,MPW-AN800-36,Splitted lot", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDB , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "5AADG101X1B,MPW-AN800-35,X1 bonding, start : 2024-11-04 , 78 days", "list_Start_ON": "2024-11-04", "list_Due_ON": "2025-01-21", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "78", "RFQPOin": "2024-11-04", "RFQPOout": "2024-11-18", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-11-04", "TOout": "2024-11-22", "PIin": "2025-01-17", "PIout": "2025-01-20", "PrePin": "", "PrePout": "", "Testout": "2025-01-20", "DataProcessOut": "2025-01-20", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "1", "list_title": "5AADG101X1B,MPW-AN800-35,X1 bonding", "list_modules": "Proto lot : FF66223 , CORE", "list_project": "MPW"}, {"list_projects_names": "6AAAN103ILA,E2.16 - HAD1 - RERUN,ILDA, start : 2025-01-15 , 8 days", "list_Start_ON": "2025-01-15", "list_Due_ON": "2025-01-23", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "8", "RFQPOin": "2024-11-24", "RFQPOout": "2024-12-04", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-15", "PIout": "2025-01-17", "PrePin": "", "PrePout": "", "Testout": "2025-01-20", "DataProcessOut": "2025-01-21", "ShipmentOut": "2025-01-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "3", "list_title": "6AAAN103ILA,E2.16 - HAD1 - RERUN,ILDA", "list_modules": "Proto lot : FF66223 , MX3 , MX3RIB , ILDA", "list_project": "EIGER"}, {"list_projects_names": "5AAEZ101M00,NGI_FOG NGI_Circuit Proto, start : 2025-03-24 , 75 days", "list_Start_ON": "2025-03-24", "list_Due_ON": "2025-06-07", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "75", "RFQPOin": "2025-04-07", "RFQPOout": "2025-04-17", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-03-24", "TOout": "2025-04-25", "PIin": "2025-04-25", "PIout": "2025-06-02", "PrePin": "", "PrePout": "", "Testout": "2025-06-03", "DataProcessOut": "2025-06-04", "ShipmentOut": "2025-06-07", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "5AAEZ101M00,NGI_FOG NGI_Circuit Proto", "list_modules": "Proto lot : FF66223 , <PERSON><PERSON> , X3", "list_project": "Dedicated run AN800"}, {"list_projects_names": "6AAFB101M00,NGI FOG_Spiral Proto, start : 2025-03-20 , 96 days", "list_Start_ON": "2025-03-20", "list_Due_ON": "2025-06-24", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "96", "RFQPOin": "2025-04-03", "RFQPOout": "2025-04-13", "ERFPOin": "2025-05-18", "ERFPOout": "2025-05-28", "TOin": "2025-03-20", "TOout": "2025-04-28", "PIin": "2025-04-28", "PIout": "2025-05-26", "PrePin": "", "PrePout": "", "Testout": "2025-05-27", "DataProcessOut": "2025-05-28", "ShipmentOut": "2025-05-31", "CSout": "2025-06-10", "BackendOut": "2025-06-24", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "6AAFB101M00,NGI FOG_Spiral Proto", "list_modules": "Proto lot : FF66223 , MX3 , ILDB , CS", "list_project": "Dedicated run AN350"}, {"list_projects_names": "6AAFC101M00,Luci, start : 2025-02-28 , 103 days", "list_Start_ON": "2025-02-28", "list_Due_ON": "2025-06-11", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "103", "RFQPOin": "2025-03-14", "RFQPOout": "2025-03-24", "ERFPOin": "2025-05-26", "ERFPOout": "2025-06-05", "TOin": "2025-02-28", "TOout": "2025-04-08", "PIin": "2025-04-08", "PIout": "2025-05-12", "PrePin": "", "PrePout": "", "Testout": "2025-05-14", "DataProcessOut": "2025-05-15", "ShipmentOut": "2025-05-18", "CSout": "2025-05-28", "BackendOut": "2025-06-11", "list_requester": "<PERSON> Yao", "WaferNumberList": "20", "list_title": "6AAFC101M00,Luci", "list_modules": "Proto lot : FF66223 , MX3 , ILDB , CS", "list_project": "Dedicated run AN350"}, {"list_projects_names": "5AAFD101M00,ZERMATT-RUN3, start : 2025-04-03 , 105 days", "list_Start_ON": "2025-04-03", "list_Due_ON": "2025-07-17", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "105", "RFQPOin": "2025-04-17", "RFQPOout": "2025-04-27", "ERFPOin": "2025-06-24", "ERFPOout": "2025-07-04", "TOin": "2025-04-03", "TOout": "2025-05-05", "PIin": "2025-05-05", "PIout": "2025-07-02", "PrePin": "", "PrePout": "", "Testout": "2025-07-03", "DataProcessOut": "2025-07-04", "ShipmentOut": "2025-07-07", "CSout": "2025-07-17", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AAFD101M00,ZERMATT-RUN3", "list_modules": "Proto lot : FF66223 , CORE , ILDA , HEATER , UCUT", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AAEJ102M00,MPW-AN800-35_2 RERUN, start : 2025-01-24 , 66 days", "list_Start_ON": "2025-01-24", "list_Due_ON": "2025-03-31", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "66", "RFQPOin": "2025-01-23", "RFQPOout": "2025-02-02", "ERFPOin": "2025-02-22", "ERFPOout": "2025-03-04", "TOin": "", "TOout": "", "PIin": "2025-01-24", "PIout": "2025-03-03", "PrePin": "", "PrePout": "", "Testout": "2025-03-04", "DataProcessOut": "2025-03-05", "ShipmentOut": "2025-03-07", "CSout": "2025-03-17", "BackendOut": "2025-03-31", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AAEJ102M00,MPW-AN800-35_2 RERUN", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDB , CS", "list_project": "MPW"}, {"list_projects_names": "5AAAR101M00,Zermatt, start : 2024-06-14 , 215 days", "list_Start_ON": "2024-06-14", "list_Due_ON": "2025-01-15", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "215", "RFQPOin": "2024-05-16", "RFQPOout": "2024-05-26", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-14", "TOout": "2024-07-02", "PIin": "2024-07-10", "PIout": "2024-09-18", "PrePin": "", "PrePout": "", "Testout": "2024-09-24", "DataProcessOut": "", "ShipmentOut": "", "CSout": "", "BackendOut": "2024-11-29", "list_requester": "<PERSON> Yao", "WaferNumberList": "12", "list_title": "5AAAR101M00,<PERSON>ermat<PERSON>", "list_modules": "Proto lot : FF66223 , CORE , ILDA , HEATER", "list_project": "Dedicated run AN800"}, {"list_projects_names": "6AABG201X3B,E2.19a,X3 bonding, start : 2024-04-01 , 235 days", "list_Start_ON": "2024-04-01", "list_Due_ON": "2024-11-22", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "235", "RFQPOin": "2024-04-27", "RFQPOout": "2024-05-07", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-04-01", "TOout": "2024-04-08", "PIin": "2024-08-14", "PIout": "2024-11-20", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-11-22", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "21", "list_title": "6AABG201X3B,E2.19a,X3 bonding", "list_modules": "Eng lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AAAB202M09,LER DEV, start : 2024-11-06 , 90 days", "list_Start_ON": "2024-11-06", "list_Due_ON": "2025-02-04", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "90", "RFQPOin": "2024-10-17", "RFQPOout": "2024-11-05", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-11-06", "PIout": "2025-01-27", "PrePin": "", "PrePout": "", "Testout": "2025-02-03", "DataProcessOut": "2025-02-04", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AAAB202M09,LER DEV", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABJ201M14,E7.2 PDN2 LOT2, start : 2024-10-03 , 234 days", "list_Start_ON": "2024-10-03", "list_Due_ON": "2025-05-25", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "234", "RFQPOin": "2024-10-03", "RFQPOout": "2024-10-23", "ERFPOin": "2025-05-03", "ERFPOout": "2025-05-12", "TOin": "2024-10-03", "TOout": "2024-10-28", "PIin": "2025-03-10", "PIout": "2025-05-09", "PrePin": "", "PrePout": "", "Testout": "2025-05-12", "DataProcessOut": "2025-05-13", "ShipmentOut": "2025-05-15", "CSout": "2025-05-25", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "23", "list_title": "6AABJ201M14,E7.2 PDN2 LOT2", "list_modules": "Eng lot : FF66223 , MX3 , IMX3RIB , ILDA , HEATER , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AABJ202M14,E7.2 PDN2 LOT1, start : 2024-11-14 , 106 days", "list_Start_ON": "2024-11-14", "list_Due_ON": "2025-02-28", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "106", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "2025-02-06", "ERFPOout": "2025-02-15", "TOin": "", "TOout": "", "PIin": "2024-11-14", "PIout": "2025-02-13", "PrePin": "", "PrePout": "", "Testout": "2025-02-15", "DataProcessOut": "2025-02-16", "ShipmentOut": "2025-02-18", "CSout": "2025-02-28", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "10", "list_title": "6AABJ202M14,E7.2 PDN2 LOT1", "list_modules": "Eng lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AABO201M11,E2.17 Wolverine3 TRIM., start : 2025-04-01 , 82 days", "list_Start_ON": "2025-04-01", "list_Due_ON": "2025-06-22", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "82", "RFQPOin": "2025-04-17", "RFQPOout": "2025-04-27", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-04-01", "TOout": "2025-05-12", "PIin": "2025-05-12", "PIout": "2025-06-16", "PrePin": "", "PrePout": "", "Testout": "2025-06-19", "DataProcessOut": "2025-06-20", "ShipmentOut": "2025-06-22", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABO201M11,E2.17 Wolverine3 TRIM.", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AABP201M11,E2.18 Wolverine3 TRIM., start : 2025-05-12 , 41 days", "list_Start_ON": "2025-05-12", "list_Due_ON": "2025-06-22", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "41", "RFQPOin": "2025-01-10", "RFQPOout": "2025-01-20", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-05-12", "PIout": "2025-06-16", "PrePin": "", "PrePout": "", "Testout": "2025-06-19", "DataProcessOut": "2025-06-20", "ShipmentOut": "2025-06-22", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABP201M11,E2.18 Wolverine3 TRIM.", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AABT201M09,E6.8, start : 2025-02-03 , 77 days", "list_Start_ON": "2025-02-03", "list_Due_ON": "2025-04-21", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "77", "RFQPOin": "2025-02-17", "RFQPOout": "2025-02-27", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-02-03", "TOout": "2025-03-14", "PIin": "2025-03-14", "PIout": "2025-04-14", "PrePin": "", "PrePout": "", "Testout": "2025-04-17", "DataProcessOut": "2025-04-18", "ShipmentOut": "2025-04-21", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABT201M09,E6.8", "list_modules": "Eng lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "4AACB201M19,MAGNIFY-RUN 1, start : 2024-07-26 , 21 days", "list_Start_ON": "2024-07-26", "list_Due_ON": "2024-08-16", "list_corridor": "Inactive (Eng)", "list_tech": "AN200", "list_cycleTime": "21", "RFQPOin": "2024-06-30", "RFQPOout": "2024-07-02", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-26", "TOout": "2024-08-13", "PIin": "2024-07-16", "PIout": "2024-08-13", "PrePin": "", "PrePout": "", "Testout": "2024-08-14", "DataProcessOut": "2024-08-14", "ShipmentOut": "2024-08-16", "CSout": "", "BackendOut": "", "list_requester": "Moustapha JAFFAL", "WaferNumberList": "12", "list_title": "4AACB201M19,MAGNIFY-RUN 1", "list_modules": "Eng lot : FF66223 , MX2", "list_project": "MAGNIFY"}, {"list_projects_names": "5AACF201M04,EWOC-run1, start : 2024-10-11 , 122 days", "list_Start_ON": "2024-10-11", "list_Due_ON": "2025-02-10", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "122", "RFQPOin": "2024-10-11", "RFQPOout": "2024-10-23", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-10-11", "TOout": "2024-10-29", "PIin": "2024-11-13", "PIout": "2025-01-29", "PrePin": "", "PrePout": "", "Testout": "2025-02-08", "DataProcessOut": "2025-02-08", "ShipmentOut": "2025-02-10", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON>", "WaferNumberList": "8", "list_title": "5AACF201M04,EWOC-run1", "list_modules": "Eng lot : FF66223 , CO<PERSON> , X3", "list_project": "6G-EWOC"}, {"list_projects_names": "6AACT201M00,EIG RIB DEV, start : 2024-08-15 , 214 days", "list_Start_ON": "2024-08-15", "list_Due_ON": "2025-03-17", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "214", "RFQPOin": "2024-08-15", "RFQPOout": "2024-09-25", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-15", "TOout": "2024-09-09", "PIin": "2024-09-18", "PIout": "2025-03-17", "PrePin": "", "PrePout": "", "Testout": "2025-02-16", "DataProcessOut": "2025-02-17", "ShipmentOut": "2025-02-19", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "9", "list_title": "6AACT201M00,EIG RIB DEV", "list_modules": "Eng lot : FF66223 , MX3 , ILDA , MX3RIB", "list_project": "EIGER"}, {"list_projects_names": "5AACU101M00,MPW-AN800-34_2, start : 2024-08-12 , 180 days", "list_Start_ON": "2024-08-12", "list_Due_ON": "2025-02-08", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "180", "RFQPOin": "2024-08-12", "RFQPOout": "2024-08-22", "ERFPOin": "2024-12-07", "ERFPOout": "2024-12-17", "TOin": "2024-08-12", "TOout": "2024-08-30", "PIin": "2024-08-30", "PIout": "2024-12-16", "PrePin": "", "PrePout": "", "Testout": "2024-12-14", "DataProcessOut": "2024-12-16", "ShipmentOut": "2024-12-23", "CSout": "2025-01-28", "BackendOut": "2025-02-08", "list_requester": "<PERSON>", "WaferNumberList": "12", "list_title": "5AACU101M00,MPW-AN800-34_2", "list_modules": "Eng lot : CORE, X2, CS, ILDB, FF66223", "list_project": "MPW"}, {"list_projects_names": "6AACV201M00,E2.12 SCTR1.c LOT1, start : 2024-07-18 , 343 days", "list_Start_ON": "2024-07-18", "list_Due_ON": "2025-06-26", "list_corridor": "Inactive (Proto)", "list_tech": "AN350", "list_cycleTime": "343", "RFQPOin": "2024-07-18", "RFQPOout": "2024-07-28", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-18", "TOout": "2024-08-12", "PIin": "2025-05-26", "PIout": "2025-06-20", "PrePin": "", "PrePout": "", "Testout": "2025-06-23", "DataProcessOut": "2025-06-24", "ShipmentOut": "2025-06-26", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACV201M00,E2.12 SCTR1.c LOT1", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "6AACV202M00,E2.12 SCTR1.c LOT2, start : 2025-05-26 , 31 days", "list_Start_ON": "2025-05-26", "list_Due_ON": "2025-06-26", "list_corridor": "Inactive (Proto)", "list_tech": "AN350", "list_cycleTime": "31", "RFQPOin": "2024-09-02", "RFQPOout": "2024-09-12", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-05-26", "PIout": "2025-06-20", "PrePin": "", "PrePout": "", "Testout": "2025-06-23", "DataProcessOut": "2025-06-24", "ShipmentOut": "2025-06-26", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACV202M00,E2.12 SCTR1.c LOT2", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "6AACV203M00,E2.12 SCTR1.c LOT3, start : 2024-09-18 , 89 days", "list_Start_ON": "2024-09-18", "list_Due_ON": "2024-12-16", "list_corridor": "Inactive (Proto)", "list_tech": "AN350", "list_cycleTime": "89", "RFQPOin": "2024-10-14", "RFQPOout": "2024-10-24", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-18", "PIout": "2024-12-12", "PrePin": "", "PrePout": "", "Testout": "2024-12-15", "DataProcessOut": "2024-12-16", "ShipmentOut": "2024-12-16", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACV203M00,E2.12 SCTR1.c LOT3", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "6AACW201M00,ETV6p2, start : 2024-09-05 , 151 days", "list_Start_ON": "2024-09-05", "list_Due_ON": "2025-02-03", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "151", "RFQPOin": "2024-09-05", "RFQPOout": "2024-10-07", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-05", "TOout": "2024-09-30", "PIin": "2024-11-29", "PIout": "2025-01-28", "PrePin": "", "PrePout": "", "Testout": "2025-01-30", "DataProcessOut": "2025-01-30", "ShipmentOut": "2025-02-03", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AACW201M00,ETV6p2", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN, ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AACX101M00,E2.15 Wolverine3 TRIM., start : 2025-05-12 , 70 days", "list_Start_ON": "2025-05-12", "list_Due_ON": "2025-07-21", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "70", "RFQPOin": "2025-01-10", "RFQPOout": "2025-01-20", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-05-12", "PIout": "2025-07-15", "PrePin": "", "PrePout": "", "Testout": "2025-07-18", "DataProcessOut": "2025-07-19", "ShipmentOut": "2025-07-21", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACX101M00,E2.15 Wolverine3 TRIM.", "list_modules": "Proto lot : FF66223-1mm , MX3 , ILDA , HEATER , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AACY101M00,EIGER LSP4, start : 2025-01-17 , 109 days", "list_Start_ON": "2025-01-17", "list_Due_ON": "2025-05-06", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "109", "RFQPOin": "2025-01-31", "RFQPOout": "2025-02-10", "ERFPOin": "2025-04-13", "ERFPOout": "2025-04-23", "TOin": "2025-01-17", "TOout": "2025-02-25", "PIin": "2025-02-25", "PIout": "2025-04-22", "PrePin": "", "PrePout": "", "Testout": "2025-04-23", "DataProcessOut": "2025-04-24", "ShipmentOut": "2025-04-26", "CSout": "2025-05-06", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AACY101M00,EIGER LSP4", "list_modules": "Proto lot : FF66223-1mm , MX3 , ILDA , HEATER , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AACZ101M00,E2.23 HAD2 LOT1, start : 2025-01-02 , 139 days", "list_Start_ON": "2025-01-02", "list_Due_ON": "2025-05-21", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "139", "RFQPOin": "2025-01-16", "RFQPOout": "2025-01-26", "ERFPOin": "2025-04-28", "ERFPOout": "2025-05-08", "TOin": "2025-01-02", "TOout": "2025-02-10", "PIin": "2025-02-20", "PIout": "2025-05-05", "PrePin": "", "PrePout": "", "Testout": "2025-05-08", "DataProcessOut": "2025-05-09", "ShipmentOut": "2025-05-11", "CSout": "2025-05-21", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACZ101M00,E2.23 HAD2 LOT1", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AACZ102M00,E2.23 HAD2 LOT2, start : 2025-04-28 , 90 days", "list_Start_ON": "2025-04-28", "list_Due_ON": "2025-07-27", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "90", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "2025-07-04", "ERFPOout": "2025-07-14", "TOin": "", "TOout": "", "PIin": "2025-04-28", "PIout": "2025-07-11", "PrePin": "", "PrePout": "", "Testout": "2025-07-14", "DataProcessOut": "2025-07-15", "ShipmentOut": "2025-07-17", "CSout": "2025-07-27", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACZ102M00,E2.23 HAD2 LOT2", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AABL202M00,E2.12 SCTR1.b, start : 2024-07-18 , 55 days", "list_Start_ON": "2024-07-18", "list_Due_ON": "2024-09-11", "list_corridor": "Inactive (Eng)", "list_tech": "AN350", "list_cycleTime": "55", "RFQPOin": "2024-07-21", "RFQPOout": "2024-07-31", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-07-18", "PIout": "2024-09-05", "PrePin": "", "PrePout": "", "Testout": "2024-09-08", "DataProcessOut": "2024-09-09", "ShipmentOut": "2024-09-11", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABL202M00,E2.12 SCTR1.b", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "5AADC201M00,MPW-LN-02, start : 2025-02-07 , 59 days", "list_Start_ON": "2025-02-07", "list_Due_ON": "2025-04-07", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "59", "RFQPOin": "2025-02-07", "RFQPOout": "2025-02-17", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-02-07", "TOout": "2025-02-25", "PIin": "2025-02-25", "PIout": "2025-04-03", "PrePin": "", "PrePout": "", "Testout": "2025-04-04", "DataProcessOut": "2025-04-05", "ShipmentOut": "2025-04-07", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON>", "WaferNumberList": "8", "list_title": "5AADC201M00,MPW-LN-02", "list_modules": "Eng lot : FF66223 , CO<PERSON> , X3", "list_project": "Dedicated run AN800"}, {"list_projects_names": "6AADD201M00,2nd Metal Layer - Shortloop, start : 2024-09-09 , 310 days", "list_Start_ON": "2024-09-09", "list_Due_ON": "2025-07-16", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "310", "RFQPOin": "2024-09-09", "RFQPOout": "2024-09-19", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-09", "TOout": "2024-10-04", "PIin": "2025-05-12", "PIout": "2025-07-10", "PrePin": "", "PrePout": "", "Testout": "2025-07-13", "DataProcessOut": "2025-07-14", "ShipmentOut": "2025-07-16", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AADD201M00,2nd Metal Layer - Shortloop", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AADE101M00,2235_29274, start : 2024-11-07 , 116 days", "list_Start_ON": "2024-11-07", "list_Due_ON": "2025-03-03", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "116", "RFQPOin": "2024-11-07", "RFQPOout": "2024-11-13", "ERFPOin": "2025-01-25", "ERFPOout": "2025-02-04", "TOin": "2024-11-07", "TOout": "2024-11-21", "PIin": "2024-12-04", "PIout": "2025-02-03", "PrePin": "", "PrePout": "", "Testout": "2025-02-04", "DataProcessOut": "2025-02-05", "ShipmentOut": "2025-02-07", "CSout": "2025-02-17", "BackendOut": "2025-03-03", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "10", "list_title": "6AADE101M00,2235_29274", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CSPLUS , UCUT", "list_project": "Dedicated run AN350"}, {"list_projects_names": "6AABL203M00,E2.12 SCTR1.b rerun, start : 2024-09-30 , 72 days", "list_Start_ON": "2024-09-30", "list_Due_ON": "2024-12-11", "list_corridor": "Inactive (Proto)", "list_tech": "AN350", "list_cycleTime": "72", "RFQPOin": "2024-09-05", "RFQPOout": "2024-09-15", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-30", "PIout": "2024-12-05", "PrePin": "", "PrePout": "", "Testout": "2024-12-08", "DataProcessOut": "2024-12-09", "ShipmentOut": "2024-12-11", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABL203M00,E2.12 SCTR1.b rerun", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "5AADG101M00,MPW-AN800-35, start : 2024-11-04 , 140 days", "list_Start_ON": "2024-11-04", "list_Due_ON": "2025-03-24", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "140", "RFQPOin": "2024-11-04", "RFQPOout": "2024-11-18", "ERFPOin": "2025-02-15", "ERFPOout": "2025-02-25", "TOin": "2024-11-04", "TOout": "2024-11-22", "PIin": "2024-10-28", "PIout": "2025-02-24", "PrePin": "", "PrePout": "", "Testout": "2025-02-25", "DataProcessOut": "2025-02-26", "ShipmentOut": "2025-02-28", "CSout": "2025-03-10", "BackendOut": "2025-03-24", "list_requester": "<PERSON>, <PERSON><PERSON><PERSON>", "WaferNumberList": "5", "list_title": "5AADG101M00,MPW-AN800-35", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "6AADH101M00,MPW-AN350-02, start : 2024-11-04 , 119 days", "list_Start_ON": "2024-11-04", "list_Due_ON": "2025-03-03", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "119", "RFQPOin": "2024-11-04", "RFQPOout": "2024-11-18", "ERFPOin": "2025-01-25", "ERFPOout": "2025-02-04", "TOin": "2024-11-04", "TOout": "2024-11-29", "PIin": "2024-12-18", "PIout": "2025-02-03", "PrePin": "", "PrePout": "", "Testout": "2025-02-04", "DataProcessOut": "2025-02-05", "ShipmentOut": "2025-02-07", "CSout": "2025-02-17", "BackendOut": "2025-03-03", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AADH101M00,MPW-AN350-02", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "5AADL101M00,MPW-AN800-37, start : 2025-04-04 , 117 days", "list_Start_ON": "2025-04-04", "list_Due_ON": "2025-07-30", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "117", "RFQPOin": "2025-04-04", "RFQPOout": "2025-04-14", "ERFPOin": "2025-06-23", "ERFPOout": "2025-07-03", "TOin": "2025-04-04", "TOout": "2025-04-22", "PIin": "2025-04-22", "PIout": "2025-07-01", "PrePin": "", "PrePout": "", "Testout": "2025-07-02", "DataProcessOut": "2025-07-03", "ShipmentOut": "2025-07-06", "CSout": "2025-07-16", "BackendOut": "2025-07-30", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AADL101M00,MPW-AN800-37", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "6AABF103MX3,E2.13b,MX3 bonding, start : 2024-08-14 , 100 days", "list_Start_ON": "2024-08-14", "list_Due_ON": "2024-11-22", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "100", "RFQPOin": "2024-05-02", "RFQPOout": "2024-05-12", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-08-14", "PIout": "2024-11-20", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-11-22", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "21", "list_title": "6AABF103MX3,E2.13b,MX3 bonding", "list_modules": "Proto lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "5AADU101M00,QPIC Run 1, start : 2024-11-28 , 151 days", "list_Start_ON": "2024-11-28", "list_Due_ON": "2025-04-28", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "151", "RFQPOin": "2024-11-22", "RFQPOout": "2024-11-22", "ERFPOin": "2025-03-22", "ERFPOout": "2025-04-01", "TOin": "2024-11-28", "TOout": "2024-12-16", "PIin": "2025-01-09", "PIout": "2025-03-15", "PrePin": "", "PrePout": "", "Testout": "2025-04-01", "DataProcessOut": "2025-04-02", "ShipmentOut": "2025-04-04", "CSout": "2025-04-14", "BackendOut": "2025-04-28", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AADU101M00,QPIC Run 1", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "QPIC"}, {"list_projects_names": "6AADZ101M00,001204 BELLE, start : 2025-03-03 , 122 days", "list_Start_ON": "2025-03-03", "list_Due_ON": "2025-07-03", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "122", "RFQPOin": "2025-03-17", "RFQPOout": "2025-03-27", "ERFPOin": "2025-02-09", "ERFPOout": "2025-02-19", "TOin": "2025-03-03", "TOout": "2025-04-11", "PIin": "2025-04-11", "PIout": "2025-06-04", "PrePin": "", "PrePout": "", "Testout": "2025-06-05", "DataProcessOut": "2025-06-06", "ShipmentOut": "2025-06-09", "CSout": "2025-06-19", "BackendOut": "2025-07-03", "list_requester": "<PERSON> Yao", "WaferNumberList": "6", "list_title": "6AADZ101M00,001204 BELLE", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CS", "list_project": "Dedicated run AN350"}, {"list_projects_names": "6AAEB101M00,LCP-01, start : 2025-01-31 , 52 days", "list_Start_ON": "2025-01-31", "list_Due_ON": "2025-03-24", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "52", "RFQPOin": "2025-01-31", "RFQPOout": "2025-02-10", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-01-31", "TOout": "2025-02-25", "PIin": "2025-02-25", "PIout": "2025-03-20", "PrePin": "", "PrePout": "", "Testout": "2025-03-21", "DataProcessOut": "2025-03-22", "ShipmentOut": "2025-03-24", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON>", "WaferNumberList": "10", "list_title": "6AAEB101M00,LCP-01", "list_modules": "Proto lot : FF66223 , MX3", "list_project": "Dedicated run AN350"}, {"list_projects_names": "6AAAC202M00,TW13 - RECIPE DOEs, start : 2025-02-16 , 49 days", "list_Start_ON": "2025-02-16", "list_Due_ON": "2025-04-06", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "49", "RFQPOin": "2024-12-06", "RFQPOout": "2024-12-16", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-02-16", "PIout": "2025-03-31", "PrePin": "", "PrePout": "", "Testout": "2025-04-03", "DataProcessOut": "2025-04-04", "ShipmentOut": "2025-04-06", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAAC202M00,TW13 - RECIPE DOEs", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AAED201M00,DV41 - STATUS LOT, start : 2025-02-10 , 66 days", "list_Start_ON": "2025-02-10", "list_Due_ON": "2025-04-17", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "66", "RFQPOin": "2025-01-27", "RFQPOout": "2025-01-31", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-02-10", "PIout": "2025-04-08", "PrePin": "", "PrePout": "", "Testout": "2025-04-13", "DataProcessOut": "2025-04-14", "ShipmentOut": "2025-04-17", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAED201M00,DV41 - STATUS LOT", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AAED202M00,DV41_LER DOE LER OPT LOT1, start : 2025-06-13 , 61 days", "list_Start_ON": "2025-06-13", "list_Due_ON": "2025-08-13", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "61", "RFQPOin": "2025-03-02", "RFQPOout": "2025-03-12", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-06-13", "PIout": "2025-08-13", "PrePin": "", "PrePout": "", "Testout": "2025-05-12", "DataProcessOut": "2025-05-13", "ShipmentOut": "2025-05-15", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAED202M00,DV41_LER DOE LER OPT LOT1", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AAAC203M00,METROLOGY_OPT DOE, start : 2025-02-03 , 48 days", "list_Start_ON": "2025-02-03", "list_Due_ON": "2025-03-23", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "48", "RFQPOin": "2025-02-03", "RFQPOout": "2025-02-07", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-02-03", "PIout": "2025-03-17", "PrePin": "", "PrePout": "", "Testout": "2025-03-20", "DataProcessOut": "2025-03-21", "ShipmentOut": "2025-03-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAAC203M00,METROLOGY_OPT DOE", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "5AADU101X1B,QPIC Run 1,X1 bonding, start : 2024-11-28 , 77 days", "list_Start_ON": "2024-11-28", "list_Due_ON": "2025-02-13", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "77", "RFQPOin": "2024-11-22", "RFQPOout": "2024-11-22", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-11-28", "TOout": "2024-12-16", "PIin": "2025-01-27", "PIout": "2025-01-29", "PrePin": "", "PrePout": "", "Testout": "2025-02-11", "DataProcessOut": "2025-02-11", "ShipmentOut": "2025-02-13", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AADU101X1B,QPIC Run 1,X1 bonding", "list_modules": "Proto lot : FF66223 , CORE", "list_project": "QPIC"}, {"list_projects_names": "6AAEI201M00,FAU_WP1.9_RI_OXIDES, start : 2025-02-16 , 56 days", "list_Start_ON": "2025-02-16", "list_Due_ON": "2025-04-13", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "56", "RFQPOin": "2024-12-08", "RFQPOout": "2024-12-18", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-02-16", "PIout": "2025-04-07", "PrePin": "", "PrePout": "", "Testout": "2025-04-10", "DataProcessOut": "2025-04-11", "ShipmentOut": "2025-04-13", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAEI201M00,FAU_WP1.9_RI_OXIDES", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AAAB203M00,LER2 DOE Cracks, start : 2024-12-18 , 36 days", "list_Start_ON": "2024-12-18", "list_Due_ON": "2025-01-23", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "36", "RFQPOin": "2024-11-21", "RFQPOout": "2024-11-22", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-12-18", "PIout": "2025-01-17", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2025-01-23", "CSout": "", "BackendOut": "", "list_requester": "Houssein EL DIRANI", "WaferNumberList": "25", "list_title": "6AAAB203M00,LER2 DOE Cracks", "list_modules": "Eng lot : FF66223 , MX3", "list_project": "EIGER"}, {"list_projects_names": "5AAEL101M00,UGENT1 Proto, start : 2025-01-20 , 70 days", "list_Start_ON": "2025-01-20", "list_Due_ON": "2025-03-31", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "70", "RFQPOin": "2025-02-03", "RFQPOout": "2025-02-13", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-01-20", "TOout": "2025-02-21", "PIin": "2025-02-21", "PIout": "2025-03-27", "PrePin": "", "PrePout": "", "Testout": "2025-03-28", "DataProcessOut": "2025-03-29", "ShipmentOut": "2025-03-31", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "5AAEL101M00,UGENT1 Proto", "list_modules": "Proto lot : FF66223 , CO<PERSON> , X2", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AAEN101M00,MPW-AN800-37, start : 2025-04-04 , 141 days", "list_Start_ON": "2025-04-04", "list_Due_ON": "2025-08-23", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "141", "RFQPOin": "2025-04-18", "RFQPOout": "2025-04-28", "ERFPOin": "2025-07-17", "ERFPOout": "2025-07-27", "TOin": "2025-04-04", "TOout": "2025-05-06", "PIin": "2025-05-06", "PIout": "2025-07-25", "PrePin": "", "PrePout": "", "Testout": "2025-07-26", "DataProcessOut": "2025-07-27", "ShipmentOut": "2025-07-30", "CSout": "2025-08-09", "BackendOut": "2025-08-23", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AAEN101M00,MPW-AN800-37", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "6AAED203M00,DV41_LER DOE LER OPT LOT2, start : 2025-10-03 , 61 days", "list_Start_ON": "2025-10-03", "list_Due_ON": "2025-12-03", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "61", "RFQPOin": "2025-05-08", "RFQPOout": "2025-05-18", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-10-03", "PIout": "2025-12-03", "PrePin": "", "PrePout": "", "Testout": "2025-07-18", "DataProcessOut": "2025-07-19", "ShipmentOut": "2025-07-21", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAED203M00,DV41_LER DOE LER OPT LOT2", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AAEQ201M00,NEW_HEATER DOE LOT, start : 2025-02-13 , 125 days", "list_Start_ON": "2025-02-13", "list_Due_ON": "2025-06-18", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "125", "RFQPOin": "2025-02-27", "RFQPOout": "2025-03-09", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-02-13", "TOout": "2025-03-24", "PIin": "2025-03-24", "PIout": "2025-06-11", "PrePin": "", "PrePout": "", "Testout": "2025-06-14", "DataProcessOut": "2025-06-15", "ShipmentOut": "2025-06-18", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAEQ201M00,NEW_HEATER DOE LOT", "list_modules": "Eng lot : FF66223 , MX3 , ILDA , HEATER", "list_project": "EIGER"}, {"list_projects_names": "6AAEI202M00,FAU_WP1.3.0/1/2_Electrostatic_Clamping_DOEs, start : 2025-01-17 , 24 days", "list_Start_ON": "2025-01-17", "list_Due_ON": "2025-02-10", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "24", "RFQPOin": "2024-12-10", "RFQPOout": "2024-12-20", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-17", "PIout": "2025-02-06", "PrePin": "", "PrePout": "", "Testout": "2025-02-07", "DataProcessOut": "2025-02-08", "ShipmentOut": "2025-02-10", "CSout": "", "BackendOut": "", "list_requester": "Moustapha JAFFAL", "WaferNumberList": "8", "list_title": "6AAEI202M00,FAU_WP1.3.0/1/2_Electrostatic_Clamping_DOEs", "list_modules": "Eng lot : FF66223 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AAEI203M00,FAU_WP1.8.0_13um_LPTEOS_TOX, start : 2025-02-16 , 41 days", "list_Start_ON": "2025-02-16", "list_Due_ON": "2025-03-29", "list_corridor": "PhotonixFAB", "list_tech": "AN350", "list_cycleTime": "41", "RFQPOin": "2024-12-06", "RFQPOout": "2024-12-16", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-02-16", "PIout": "2025-03-24", "PrePin": "", "PrePout": "", "Testout": "2025-03-26", "DataProcessOut": "2025-03-27", "ShipmentOut": "2025-03-29", "CSout": "", "BackendOut": "", "list_requester": "Moustapha JAFFAL", "WaferNumberList": "12", "list_title": "6AAEI203M00,FAU_WP1.8.0_13um_LPTEOS_TOX", "list_modules": "Eng lot : FF66223 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AAAM203M00,FAU_WP1.7.0_ILO_Plate_Height, start : 2025-01-20 , 16 days", "list_Start_ON": "2025-01-20", "list_Due_ON": "2025-02-05", "list_corridor": "PhotonixFAB", "list_tech": "AN350", "list_cycleTime": "16", "RFQPOin": "2025-01-15", "RFQPOout": "2025-01-15", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-20", "PIout": "2025-02-05", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "Moustapha JAFFAL", "WaferNumberList": "25", "list_title": "6AAAM203M00,FAU_WP1.7.0_ILO_Plate_Height", "list_modules": "Eng lot : FF66223 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AACU204M00,FAU_WP1.6.1_SiN_Plate_Technology, start : 2025-02-03 , 41 days", "list_Start_ON": "2025-02-03", "list_Due_ON": "2025-03-16", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "41", "RFQPOin": "2025-01-14", "RFQPOout": "2025-01-17", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-02-03", "PIout": "2025-02-17", "PrePin": "", "PrePout": "", "Testout": "2025-03-12", "DataProcessOut": "2025-03-13", "ShipmentOut": "2025-03-16", "CSout": "", "BackendOut": "", "list_requester": "Moustapha JAFFAL", "WaferNumberList": "16", "list_title": "6AACU204M00,FAU_WP1.6.1_SiN_Plate_Technology", "list_modules": "Eng lot : FF66223 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AABS205M00,E6.7 MERLIN5 RERUN2, start : 2025-01-22 , 32 days", "list_Start_ON": "2025-01-22", "list_Due_ON": "2025-02-23", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "32", "RFQPOin": "2025-01-22", "RFQPOout": "2025-01-24", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-22", "PIout": "2025-02-17", "PrePin": "", "PrePout": "", "Testout": "2025-02-20", "DataProcessOut": "2025-02-21", "ShipmentOut": "2025-02-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AABS205M00,E6.7 MERLIN5 RERUN2", "list_modules": "Eng lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AABS206M00,E6.7 MERLIN5 RERUN3, start : 2025-01-22 , 56 days", "list_Start_ON": "2025-01-22", "list_Due_ON": "2025-03-19", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "56", "RFQPOin": "2025-03-09", "RFQPOout": "2025-03-19", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-22", "PIout": "2025-02-17", "PrePin": "", "PrePout": "", "Testout": "2025-02-20", "DataProcessOut": "2025-02-21", "ShipmentOut": "2025-02-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AABS206M00,E6.7 MERLIN5 RERUN3", "list_modules": "Eng lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AAES201M00,AN350_X0_IMPLEMENTATION, start : 2025-02-20 , 89 days", "list_Start_ON": "2025-02-20", "list_Due_ON": "2025-05-20", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "89", "RFQPOin": "2025-03-06", "RFQPOout": "2025-03-16", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-02-20", "TOout": "2025-03-31", "PIin": "2025-03-14", "PIout": "2025-05-20", "PrePin": "", "PrePout": "", "Testout": "2025-05-01", "DataProcessOut": "2025-05-02", "ShipmentOut": "2025-05-05", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AAES201M00,AN350_X0_IMPLEMENTATION", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AAET101M00,TV7.1, start : 2025-02-23 , 89 days", "list_Start_ON": "2025-02-23", "list_Due_ON": "2025-05-23", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "89", "RFQPOin": "2025-03-09", "RFQPOout": "2025-03-19", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-02-23", "TOout": "2025-04-03", "PIin": "2025-04-03", "PIout": "2025-05-16", "PrePin": "", "PrePout": "", "Testout": "2025-05-19", "DataProcessOut": "2025-05-20", "ShipmentOut": "2025-05-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AAET101M00,TV7.1", "list_modules": "Proto lot : FF66223-1mm , RESIST_OPEN , MX3 , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "6AAEU101M00,TNG3, start : 2025-02-28 , 89 days", "list_Start_ON": "2025-02-28", "list_Due_ON": "2025-05-28", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "89", "RFQPOin": "2025-03-14", "RFQPOout": "2025-03-24", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-02-28", "TOout": "2025-04-08", "PIin": "2025-04-08", "PIout": "2025-05-21", "PrePin": "", "PrePout": "", "Testout": "2025-05-24", "DataProcessOut": "2025-05-25", "ShipmentOut": "2025-05-28", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AAEU101M00,TNG3", "list_modules": "Proto lot : FF66223-1mm , RESIST_OPEN , MX3 , BOWPOLYSI", "list_project": "EIGER"}, {"list_projects_names": "6AACV204M00,SCTR_PVD_DOE, start : 2025-02-03 , 101 days", "list_Start_ON": "2025-02-03", "list_Due_ON": "2025-05-15", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "101", "RFQPOin": "2025-05-05", "RFQPOout": "2025-05-15", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-02-03", "PIout": "2025-04-07", "PrePin": "", "PrePout": "", "Testout": "2025-04-10", "DataProcessOut": "2025-04-11", "ShipmentOut": "2025-04-13", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "6AACV204M00,SCTR_PVD_DOE", "list_modules": "Eng lot : FF66223-1mm , RESIST_OPEN , MX3 , BOWPOLYSI , ILDA", "list_project": "EIGER"}, {"list_projects_names": "5AAEX101M00,Proto, start : 2025-04-11 , 141 days", "list_Start_ON": "2025-04-11", "list_Due_ON": "2025-08-30", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "141", "RFQPOin": "2025-04-25", "RFQPOout": "2025-05-05", "ERFPOin": "2025-07-24", "ERFPOout": "2025-08-03", "TOin": "2025-04-11", "TOout": "2025-05-13", "PIin": "2025-05-13", "PIout": "2025-08-01", "PrePin": "", "PrePout": "", "Testout": "2025-08-02", "DataProcessOut": "2025-08-03", "ShipmentOut": "2025-08-06", "CSout": "2025-08-16", "BackendOut": "2025-08-30", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "5AAEX101M00,Proto", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "CSOC"}, {"list_projects_names": "6AAEY101M00,JURA-LN1, start : 2025-04-07 , 91 days", "list_Start_ON": "2025-04-07", "list_Due_ON": "2025-07-07", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "91", "RFQPOin": "2025-04-21", "RFQPOout": "2025-05-01", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-04-07", "TOout": "2025-05-16", "PIin": "2025-05-16", "PIout": "2025-07-01", "PrePin": "", "PrePout": "", "Testout": "2025-07-03", "DataProcessOut": "2025-07-04", "ShipmentOut": "2025-07-07", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "12", "list_title": "6AAEY101M00,JURA-LN1", "list_modules": "Proto lot : FF66223 , MX3 , MX3X2 , ILDA", "list_project": "<PERSON><PERSON>"}, {"list_projects_names": "5AAFA101M00,NGI_FOG NGI_Circuit Proto, start : 2025-02-28 , 75 days", "list_Start_ON": "2025-02-28", "list_Due_ON": "2025-05-14", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "75", "RFQPOin": "2025-03-14", "RFQPOout": "2025-03-24", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-02-28", "TOout": "2025-04-01", "PIin": "2025-04-01", "PIout": "2025-05-09", "PrePin": "", "PrePout": "", "Testout": "2025-05-10", "DataProcessOut": "2025-05-11", "ShipmentOut": "2025-05-14", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "5AAFA101M00,NGI_FOG NGI_Circuit Proto", "list_modules": "Proto lot : FF66223 , <PERSON><PERSON> , X3", "list_project": "Dedicated run AN800"}, {"list_projects_names": "6AABS207M00,MERLIN5 RERUN4, start : 2025-01-29 , 103 days", "list_Start_ON": "2025-01-29", "list_Due_ON": "2025-05-12", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "103", "RFQPOin": "2025-03-28", "RFQPOout": "2025-04-07", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-29", "PIout": "2025-02-24", "PrePin": "", "PrePout": "", "Testout": "2025-05-08", "DataProcessOut": "2025-05-09", "ShipmentOut": "2025-05-12", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABS207M00,MERLIN5 RERUN4", "list_modules": "Eng lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "5AACL201M17,PF_WP8_LNOI, start : 2025-03-15 , 66 days", "list_Start_ON": "2025-03-15", "list_Due_ON": "2025-05-20", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "66", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-03-15", "TOout": "2025-04-16", "PIin": "2025-04-16", "PIout": "2025-05-13", "PrePin": "", "PrePout": "", "Testout": "2025-05-16", "DataProcessOut": "2025-05-17", "ShipmentOut": "2025-05-20", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AACL201M17,PF_WP8_LNOI", "list_modules": "Eng lot : FF66223 , CORE", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "5AACQ201M00,PF_WP7/8_PD_02, start : 2024-09-02 , 249 days", "list_Start_ON": "2024-09-02", "list_Due_ON": "2025-05-09", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "249", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-02", "TOout": "2024-09-20", "PIin": "2025-01-09", "PIout": "2025-04-09", "PrePin": "", "PrePout": "", "Testout": "2025-04-12", "DataProcessOut": "2025-04-13", "ShipmentOut": "2025-04-15", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AACQ201M00,PF_WP7/8_PD_02", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "5AACQ202M00,PF_WP7/8_PD_02, start : 2025-01-09 , 115 days", "list_Start_ON": "2025-01-09", "list_Due_ON": "2025-05-04", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "115", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-09", "PIout": "2025-04-09", "PrePin": "", "PrePout": "", "Testout": "2025-04-12", "DataProcessOut": "2025-04-13", "ShipmentOut": "2025-04-15", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AACQ202M00,PF_WP7/8_PD_02", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "6AACR201M00,PF_WP4_LOCA_S, start : 2024-12-26 , 132 days", "list_Start_ON": "2024-12-26", "list_Due_ON": "2025-05-07", "list_corridor": "PhotonixFAB", "list_tech": "AN350", "list_cycleTime": "132", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "2025-04-01", "ERFPOout": "2025-04-10", "TOin": "2024-12-26", "TOout": "2025-02-03", "PIin": "2025-01-20", "PIout": "2025-04-07", "PrePin": "", "PrePout": "", "Testout": "2025-04-10", "DataProcessOut": "2025-04-11", "ShipmentOut": "2025-04-13", "CSout": "2025-04-23", "BackendOut": "2025-05-07", "list_requester": "Houssein EL DIRANI", "WaferNumberList": "25", "list_title": "6AACR201M00,PF_WP4_LOCA_S", "list_modules": "Eng lot : FF66223 , MX3 , MX3RIB , MX3X2 , LOCA-S , CS", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "5AADA201M00,photonixFAB_WP8_01, start : 2024-07-29 , 214 days", "list_Start_ON": "2024-07-29", "list_Due_ON": "2025-02-28", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "214", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-29", "TOout": "2024-08-16", "PIin": "2024-09-02", "PIout": "2025-02-28", "PrePin": "", "PrePout": "", "Testout": "2025-01-02", "DataProcessOut": "2025-01-03", "ShipmentOut": "2025-01-05", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "14", "list_title": "5AADA201M00,photonixFAB_WP8_01", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDB , LOCA", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "5AAER201M00,PhotonixFAB_WP4.1_Lot1, start : 2024-02-15 , 35 days", "list_Start_ON": "2024-02-15", "list_Due_ON": "2024-03-21", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "35", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-02-15", "PIout": "2024-03-19", "PrePin": "", "PrePout": "", "Testout": "2024-03-20", "DataProcessOut": "2024-03-21", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AAER201M00,PhotonixFAB_WP4.1_Lot1", "list_modules": "Eng lot : FF66223 , CORE , ILDA", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "6AAAM204M00,FAU_WP1.7.0_ILO_Plate_Height (LOT2), start : 2025-01-20 , 16 days", "list_Start_ON": "2025-01-20", "list_Due_ON": "2025-02-05", "list_corridor": "PhotonixFAB", "list_tech": "AN350", "list_cycleTime": "16", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2025-01-20", "PIout": "2025-02-05", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "Moustapha JAFFAL", "WaferNumberList": "25", "list_title": "6AAAM204M00,FAU_WP1.7.0_ILO_Plate_Height (LOT2)", "list_modules": "Eng lot : FF66223 , MX3", "list_project": "EIGER"}]}