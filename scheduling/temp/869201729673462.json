{"df_asana": [{"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "464436269489794", "MaskList": "AAA", "ProjectMaskList": "05-46 EIGER 2.10", "LotToConfirmList": "4AAAZ001M19,Magnify", "LotToSplitList": "5AABA101M01", "PROJECTS": "test", "LotToManageList": "5AABA101M01,test"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "467496932920604", "MaskList": "AAB", "ProjectMaskList": "05-50 LER1", "LotToConfirmList": null, "LotToSplitList": "6AAAY001M08", "PROJECTS": "EIGR8p4 second lot ", "LotToManageList": "4AAAZ001M19,Magnify"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "489733785249552", "MaskList": "AAC", "ProjectMaskList": "05-50 LER2", "LotToConfirmList": null, "LotToSplitList": "5AAAX201M17", "PROJECTS": "LGT labs PD MTP", "LotToManageList": "6AAAY001M08,EIGR8p4 second lot "}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "513842320205279", "MaskList": "AAD", "ProjectMaskList": "05-54 EIGER 2.11 UCUT", "LotToConfirmList": null, "LotToSplitList": "5AAAW201M02", "PROJECTS": "Glider 3.1", "LotToManageList": "5AAAX201M17,LGT labs PD MTP"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "794540907829992", "MaskList": "AAE", "ProjectMaskList": "05-56 MPW31", "LotToConfirmList": null, "LotToSplitList": "6AAAV201M11", "PROJECTS": "EIGER 2.15", "LotToManageList": "5AAAW201M02,Glider 3.1"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "803914941875841", "MaskList": "AAF", "ProjectMaskList": "Rerun 05-43 MPW29a", "LotToConfirmList": null, "LotToSplitList": "6AAAU101M11", "PROJECTS": "MPW32C", "LotToManageList": "6AAAV201M11,EIGER 2.15"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "869201729673462", "MaskList": "AAG", "ProjectMaskList": "05-55 UCSD", "LotToConfirmList": null, "LotToSplitList": "5AAAT101M03", "PROJECTS": "MPW33AB", "LotToManageList": "6AAAU101M11,MPW32C"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "669147563988443", "MaskList": "AAH", "ProjectMaskList": "05-61_LIGH", "LotToConfirmList": null, "LotToSplitList": "5AAAS101M16", "PROJECTS": "Dynamos 2", "LotToManageList": "5AAAT101M03,MPW33AB"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1131443696074648", "MaskList": "AAI", "ProjectMaskList": "05-60_PAKH", "LotToConfirmList": null, "LotToSplitList": "5AAAR101M00", "PROJECTS": "ZTEP", "LotToManageList": "5AAAS101M16,Dynamos 2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1125291682985446", "MaskList": "AAJ", "ProjectMaskList": "05-62 EIGER 2.13", "LotToConfirmList": null, "LotToSplitList": "6AAAQ102M14", "PROJECTS": "EIGER 7.2", "LotToManageList": "5AAAR101M00,ZTEP"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1160066995528264", "MaskList": "AAK", "ProjectMaskList": "05-57 EIGER 2.12", "LotToConfirmList": null, "LotToSplitList": "6AAAQ101M14", "PROJECTS": "EIGER 7.2", "LotToManageList": "6AAAQ102M14,EIGER 7.2"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1171139149669535", "MaskList": "AAL", "ProjectMaskList": "MPW32AB", "LotToConfirmList": null, "LotToSplitList": "6AAAO001M10", "PROJECTS": "<PERSON><PERSON><PERSON>", "LotToManageList": "6AAAQ101M14,EIGER 7.2"}, {"requester_names_list": "Ana Catarina <PERSON>", "requester_gid_list": "1183107046965382", "MaskList": "AAM", "ProjectMaskList": "EIGER 2.14", "LotToConfirmList": null, "LotToSplitList": "6AAAN102M14", "PROJECTS": "EIGER 2.16", "LotToManageList": "6AAAO001M10,<PERSON><PERSON><PERSON>"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1191400555194352", "MaskList": "AAN", "ProjectMaskList": "EIGER 2.16", "LotToConfirmList": null, "LotToSplitList": "6AAAN101M14", "PROJECTS": "EIGER 2.16", "LotToManageList": "6AAAN102M14,<PERSON>IG<PERSON> 2.16"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1193626179934205", "MaskList": "AAO", "ProjectMaskList": "<PERSON><PERSON><PERSON>", "LotToConfirmList": null, "LotToSplitList": "6AAAM201M11", "PROJECTS": "EIGER 2.14", "LotToManageList": "6AAAN101M14,<PERSON>IG<PERSON> 2.16"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199110939357003", "MaskList": "AAQ", "ProjectMaskList": "EIGER 7.2", "LotToConfirmList": null, "LotToSplitList": "5AAAL101M03", "PROJECTS": "MPW32AB", "LotToManageList": "6AAAM201M11,<PERSON>IG<PERSON> 2.14"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199394325037760", "MaskList": "AAR", "ProjectMaskList": "ZTEP", "LotToConfirmList": null, "LotToSplitList": "6AAAK201M10", "PROJECTS": "05-57 EIGER 2.12", "LotToManageList": "5AAAL101M03,MPW32AB"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1199990271835482", "MaskList": "AAS", "ProjectMaskList": "Dynamos 2", "LotToConfirmList": null, "LotToSplitList": "6AAAJ102M08", "PROJECTS": "05-62 EIGER 2.13", "LotToManageList": "6AAAK201M10,05-57 EIGER 2.12"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1200295708067936", "MaskList": "AAT", "ProjectMaskList": "MPW33AB", "LotToConfirmList": null, "LotToSplitList": "6AAAJ101M08", "PROJECTS": "05-62 EIGER 2.13", "LotToManageList": "6AAAJ102M08,05-62 EIGER 2.13"}, {"requester_names_list": "<PERSON> Yao", "requester_gid_list": "1200692689203662", "MaskList": "AAU", "ProjectMaskList": "MPW32C", "LotToConfirmList": null, "LotToSplitList": "6AAAI001M08", "PROJECTS": "05-60_PAKH", "LotToManageList": "6AAAJ101M08,05-62 EIGER 2.13"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201328860961631", "MaskList": "AAV", "ProjectMaskList": "EIGER 2.15", "LotToConfirmList": null, "LotToSplitList": "4AAAH101M19", "PROJECTS": "05-61_LIGH", "LotToManageList": "6AAAI001M08,05-60_PAKH"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201466301932731", "MaskList": "AAW", "ProjectMaskList": "Glider 3.1", "LotToConfirmList": null, "LotToSplitList": "6AAAD202M11", "PROJECTS": "05-54 EIGER 2.11 UCUT", "LotToManageList": "4AAAH101M19,05-61_LIGH"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201752762206320", "MaskList": "AAX", "ProjectMaskList": "LGT labs PD MTP", "LotToConfirmList": null, "LotToSplitList": "5AAAG101M04", "PROJECTS": "05-55 UCSD", "LotToManageList": "6AAAD202M11,05-54 EIGER 2.11 UCUT"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1201788320590322", "MaskList": "AAY", "ProjectMaskList": "EIGR8p4 second lot ", "LotToConfirmList": null, "LotToSplitList": "5AAAF101M03", "PROJECTS": "Rerun 05-43 MPW29a", "LotToManageList": "5AAAG101M04,05-55 UCSD"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201830090104290", "MaskList": "AAZ", "ProjectMaskList": "Magnify", "LotToConfirmList": null, "LotToSplitList": "5AAAE101M02", "PROJECTS": "05-56 MPW31", "LotToManageList": "5AAAF101M03,<PERSON><PERSON> 05-43 MPW29a"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201899025933306", "MaskList": "ABA", "ProjectMaskList": "test", "LotToConfirmList": null, "LotToSplitList": "6AAAC201M09", "PROJECTS": "05-50 LER2", "LotToManageList": "5AAAE101X1B,05-56 MPW31"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201922419740872", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": "6AAAA201M14", "PROJECTS": "05-46 EIGER 2.10", "LotToManageList": "5AAAE101M02,05-56 MPW31"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201964818841249", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AAAE101M02,05-56 MPW31 ILDB"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1202129345004455", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AAAC201M09,05-50 LER2"}, {"requester_names_list": "Houssein EL DIRANI", "requester_gid_list": "1202135074352084", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AAAA201M14,05-46 EIGER 2.10"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202444549987515", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1202684057491700", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202907572860628", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203079565264253", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1203283724869310", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203601372370011", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "Audie <PERSON>", "requester_gid_list": "1203873691444950", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1203869571959179", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204469536661025", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204562481875818", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "Moustapha JAFFAL", "requester_gid_list": "1204562482451099", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1204325331381742", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1204701988903544", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "Ulysse Fanda", "requester_gid_list": "1204701989427483", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204950886823380", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204968822616977", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "Viphretuo Mere", "requester_gid_list": "1204971721140670", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1205085908688191", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205137449747223", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201278474361148", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205595234938764", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205709973073733", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "Yann K<PERSON>ZY<PERSON>", "requester_gid_list": "1204164213558726", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205847382524398", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1205874105867614", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1206225376712932", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206468476801011", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477227688", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477262721", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206660149483972", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660407180541", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408294625", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<EMAIL>", "requester_gid_list": "1206660408413444", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408470048", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408540230", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}], "capacity_data_frame": [{"Lot Type": "Prototype", "Number of Lots": 6.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN800", "Priority": "Low", "Production date in": "Monday", "Projects": "MPW", "Task delay Update min (d)": 6.0, "Task advance update max (d)": 6.0, "max Lot reservation (d)": 14.0}, {"Lot Type": "Engineering", "Number of Lots": 6.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN350", "Priority": "Normal", "Production date in": "Tuesday", "Projects": "EIGER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Pre-production", "Number of Lots": 4.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 4.0, "Wafer number max": 25, "Technology": "AN200", "Priority": "High", "Production date in": "Wednesday", "Projects": "GLIDER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Proto-Engineering", "Number of Lots": 2.0, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": "Thursday", "Projects": "PHOTONIXFAB", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": "Friday", "Projects": "MAGNIFY", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "DYNAMOS", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "QPIC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "FEMTOCHIP", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "Dedicated run AN800", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "<PERSON><PERSON>", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "PhotonHub", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": NaN, "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": NaN, "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": "                                                     ", "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": NaN, "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}], "OP_data_frame": [{"Technology": "AN800", "Modules": "X1+Heater", "Pre-production(d)": 0, "Production(d)": 70.0, "Production-Rerun(d)": 53.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 1.0, "CS (d)": 12.0, "Grinding (d)": 14.0}, {"Technology": "AN800", "Modules": "X1+ILDB", "Pre-production(d)": 0, "Production(d)": 42.0, "Production-Rerun(d)": 30.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM EIGR", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater", "Pre-production(d)": 0, "Production(d)": 85.0, "Production-Rerun(d)": 64.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 70.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3 bonding", "Pre-production(d)": 0, "Production(d)": 60.0, "Production-Rerun(d)": 48.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 74.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+ILDB", "Pre-production(d)": 0, "Production(d)": 57.0, "Production-Rerun(d)": 45.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+ILDB", "Pre-production(d)": 0, "Production(d)": 77.0, "Production-Rerun(d)": 61.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 Bonding ", "Pre-production(d)": 7, "Production(d)": 33.0, "Production-Rerun(d)": 30.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDA", "Pre-production(d)": 7, "Production(d)": 37.0, "Production-Rerun(d)": 34.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDB", "Pre-production(d)": 7, "Production(d)": 40.0, "Production-Rerun(d)": 37.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + HEATER", "Pre-production(d)": 7, "Production(d)": 68.0, "Production-Rerun(d)": 56.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDA", "Pre-production(d)": 7, "Production(d)": 53.0, "Production-Rerun(d)": 50.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDB", "Pre-production(d)": 7, "Production(d)": 56.0, "Production-Rerun(d)": 53.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB +HEATER", "Pre-production(d)": 7, "Production(d)": 83.0, "Production-Rerun(d)": 71.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 104.0, "Production-Rerun(d)": 104.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 95.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1 bonding", "Pre-production(d)": 0, "Production(d)": 35.0, "Production-Rerun(d)": 35.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "SiN dep ", "Pre-production(d)": 0, "Production(d)": 7.0, "Production-Rerun(d)": 7.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN200", "Modules": "MX2 Bonding", "Pre-production(d)": 0, "Production(d)": 25.0, "Production-Rerun(d)": 21.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": " ", "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}], "ENG_data_frame": [{"Technology": "AN800", "Modules": "X1+Heater", "Pre-production(d)": 0, "Production(d)": 70.0, "Production-Rerun(d)": 53.0, "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 1.0, "CS (d)": 12.0, "Grinding (d)": 14.0}, {"Technology": "AN800", "Modules": "X1+ILDB", "Pre-production(d)": 0, "Production(d)": 42.0, "Production-Rerun(d)": 30.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM EIGR", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater", "Pre-production(d)": 0, "Production(d)": 85.0, "Production-Rerun(d)": 64.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 70.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3 bonding", "Pre-production(d)": 0, "Production(d)": 60.0, "Production-Rerun(d)": 48.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X3+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 74.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+ILDB", "Pre-production(d)": 0, "Production(d)": 57.0, "Production-Rerun(d)": 45.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+ILDB", "Pre-production(d)": 0, "Production(d)": 77.0, "Production-Rerun(d)": 61.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 Bonding ", "Pre-production(d)": 7, "Production(d)": 33.0, "Production-Rerun(d)": 30.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDA", "Pre-production(d)": 7, "Production(d)": 37.0, "Production-Rerun(d)": 34.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + ILDB", "Pre-production(d)": 7, "Production(d)": 40.0, "Production-Rerun(d)": 37.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM G5.1(Standard)", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + HEATER", "Pre-production(d)": 7, "Production(d)": 68.0, "Production-Rerun(d)": 56.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDA", "Pre-production(d)": 7, "Production(d)": 53.0, "Production-Rerun(d)": 50.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB + ILDB", "Pre-production(d)": 7, "Production(d)": 56.0, "Production-Rerun(d)": 53.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "MX3 + RIB +HEATER", "Pre-production(d)": 7, "Production(d)": 83.0, "Production-Rerun(d)": 71.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater+LOCA-i", "Pre-production(d)": 0, "Production(d)": 104.0, "Production-Rerun(d)": 104.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1+X2+RIB+Heater", "Pre-production(d)": 0, "Production(d)": 95.0, "Production-Rerun(d)": 95.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN800", "Modules": "X1 bonding", "Pre-production(d)": 0, "Production(d)": 35.0, "Production-Rerun(d)": 35.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN350", "Modules": "SiN dep ", "Pre-production(d)": 0, "Production(d)": 7.0, "Production-Rerun(d)": 7.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": "AN200", "Modules": "MX2 Bonding", "Pre-production(d)": 0, "Production(d)": 25.0, "Production-Rerun(d)": 21.0, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": NaN, "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}, {"Technology": NaN, "Modules": NaN, "Pre-production(d)": " ", "Production(d)": NaN, "Production-Rerun(d)": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Grinding (d)": NaN}], "AsanaProject_data_frame": [{"Asana project": "Mask assembly", "project gid": 1203927479554343, "Portfolio": "Prototype", "Portfolio gid": 1206397258164834.0, "Talos version": "1.0.0"}, {"Asana project": "RFQ-PO", "project gid": 1206397258493014, "Portfolio": "Engineering", "Portfolio gid": 1206397258164837.0, "Talos version": NaN}, {"Asana project": "PI", "project gid": 1206397258492997, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Testing", "project gid": 1206397258164847, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Data processing", "project gid": 1206397258493001, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Shipment", "project gid": 1206397258493005, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "CS", "project gid": 1206521529181046, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Grinding", "project gid": 1206521529181050, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}], "LotNumber_data_frame": [{"Technology": "AN800", "Technology code": "5A", "Lot Type": "Pre-production", "Lot Type code": 0.0, "Modules": "X1+Heater", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": "LGT code", "Unnamed: 10": "Technology", "Mask Number (3 digits)": "LGT Code ", "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": "XFAB Mask ref", "Lot type (1 digit)": "LGT code", "Unnamed: 16": "Lot type", "Run Number (2 digits)": "LGT Code ", "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": "LGT code", "Unnamed: 21": NaN, "Unnamed: 22": "Process step"}, {"Technology": "AN350", "Technology code": "6A", "Lot Type": "Prototype", "Lot Type code": 1.0, "Modules": "X1+Heater", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": "5A", "Unnamed: 10": "AN800", "Mask Number (3 digits)": "A", "Unnamed: 12": "A", "Unnamed: 13": "A", "Unnamed: 14": NaN, "Lot type (1 digit)": 0, "Unnamed: 16": "Pre-production", "Run Number (2 digits)": 0, "Unnamed: 18": 0.0, "Unnamed: 19": 0.0, "Process step (2digits)": 0, "Unnamed: 21": 0.0, "Unnamed: 22": "End"}, {"Technology": "AN200", "Technology code": "4A", "Lot Type": "Engineering", "Lot Type code": 2.0, "Modules": "X1+ILDB", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": "6A", "Unnamed: 10": "AN350", "Mask Number (3 digits)": 0, "Unnamed: 12": 0, "Unnamed: 13": 1, "Unnamed: 14": NaN, "Lot type (1 digit)": 1, "Unnamed: 16": "Prototype", "Run Number (2 digits)": 0, "Unnamed: 18": 0.0, "Unnamed: 19": 1.0, "Process step (2digits)": 0, "Unnamed: 21": 1.0, "Unnamed: 22": "X1"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": "Production", "Lot Type code": 3.0, "Modules": "X1+ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": 0, "Unnamed: 12": 0, "Unnamed: 13": 2, "Unnamed: 14": NaN, "Lot type (1 digit)": 2, "Unnamed: 16": "Engineering", "Run Number (2 digits)": 0, "Unnamed: 18": 0.0, "Unnamed: 19": 2.0, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": "X2"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": 3, "Unnamed: 16": "Production", "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": "HEATER"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X2 bonding", "Layer code": "X2B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "HEATER", "Layer code": "HET", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "X1 bonding", "Layer code": "X1B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": "Example", "Mask Number (3 digits)": "05-044-1-00-2", "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "X1 RIB bonding", "Layer code": "1RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "X3 bonding", "Layer code": "X3B", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "X3 RIB bonding", "Layer code": "3RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "X3 RIB bonding", "Layer code": "3RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "X3 RIB bonding", "Layer code": "3RB", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "ILDA", "Layer code": "ILA", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+Heater", "Layer": "END", "Layer code": "M00", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+ILDB", "Layer": "END", "Layer code": "M01", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "END", "Layer code": "M02", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "END", "Layer code": "M03", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3 bonding", "Layer": "END", "Layer code": "M04", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "END", "Layer code": "M05", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "END", "Layer code": "M06", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "END", "Layer code": "M07", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 Bonding ", "Layer": "END", "Layer code": "M08", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "END", "Layer code": "M09", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "END", "Layer code": "M10", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "END", "Layer code": "M11", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "END", "Layer code": "M12", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "END", "Layer code": "M13", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "END", "Layer code": "M14", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater+LOCA-i", "Layer": "END", "Layer code": "M15", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater", "Layer": "END", "Layer code": "M16", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1 bonding", "Layer": "END", "Layer code": "M17", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "SiN dep", "Layer": "END", "Layer code": "M18", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX2 Bonding", "Layer": "END", "Layer code": "M19", "Layer state": 100, "Unnamed: 8": NaN, "Technology (2digits)": NaN, "Unnamed: 10": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 12": NaN, "Unnamed: 13": NaN, "Unnamed: 14": NaN, "Lot type (1 digit)": NaN, "Unnamed: 16": NaN, "Run Number (2 digits)": NaN, "Unnamed: 18": NaN, "Unnamed: 19": NaN, "Process step (2digits)": NaN, "Unnamed: 21": NaN, "Unnamed: 22": NaN}], "Email_frame": [{"Requester": "<PERSON><PERSON>", "Acronym": "dav"}, {"Requester": "<PERSON>", "Acronym": "maz"}, {"Requester": "<PERSON>", "Acronym": "mwg"}, {"Requester": "<PERSON><PERSON>", "Acronym": "gbn"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "kal"}, {"Requester": "<PERSON>", "Acronym": "tat"}, {"Requester": "<PERSON>", "Acronym": "ast"}, {"Requester": "<PERSON><PERSON>", "Acronym": "fin"}, {"Requester": "<PERSON>", "Acronym": "tom"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "arc"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mar"}, {"Requester": "Ana Catarina <PERSON>", "Acronym": "acm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "ozp"}, {"Requester": "<PERSON>", "Acronym": "hry"}, {"Requester": "<PERSON>", "Acronym": "anb"}, {"Requester": "<PERSON>", "Acronym": "anf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "jeg"}, {"Requester": "<PERSON>", "Acronym": "jfc"}, {"Requester": "<PERSON> Yao", "Acronym": "cay"}, {"Requester": "<PERSON>", "Acronym": "piw"}, {"Requester": "<PERSON><PERSON>", "Acronym": "cob"}, {"Requester": "<PERSON>", "Acronym": "jdf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "plz"}, {"Requester": "<PERSON>", "Acronym": "sab"}, {"Requester": "<PERSON>", "Acronym": "stm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "asa"}, {"Requester": "<PERSON>", "Acronym": "axp"}, {"Requester": "<PERSON>", "Acronym": "gug"}, {"Requester": "Houssein EL DIRANI", "Acronym": "hod"}, {"Requester": "<PERSON><PERSON>", "Acronym": "dab"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON>", "Acronym": "abh"}, {"Requester": "<PERSON><PERSON>", "Acronym": "lde"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "alf"}, {"Requester": "<PERSON>", "Acronym": "cls"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "tol"}, {"Requester": "Audie <PERSON>", "Acronym": "aye"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mat"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "stc"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "git"}, {"Requester": "Moustapha JAFFAL", "Acronym": "moj"}, {"Requester": "<PERSON>", "Acronym": "anv"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Acronym": "abr"}, {"Requester": "Ulysse Fanda", "Acronym": "ulf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "chr"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "khk"}, {"Requester": "Viphretuo Mere", "Acronym": "vim"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "zba"}, {"Requester": "<PERSON>", "Acronym": "fov"}, {"Requester": "<PERSON><PERSON>", "Acronym": "jod"}, {"Requester": "<PERSON>", "Acronym": "frg"}, {"Requester": "<PERSON>", "Acronym": "joj"}, {"Requester": "Yann K<PERSON>ZY<PERSON>", "Acronym": "yak"}, {"Requester": "<PERSON>", "Acronym": "gab"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "swp"}, {"Requester": "<PERSON><PERSON>", "Acronym": "yil"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "sds"}, {"Requester": "<PERSON>", "Acronym": "mab"}, {"Requester": "<PERSON>", "Acronym": "ann"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "myl"}, {"Requester": "<PERSON>", "Acronym": "boz"}, {"Requester": "<PERSON>", "Acronym": "alb"}, {"Requester": "<EMAIL>", "Acronym": "adi"}, {"Requester": "<PERSON>", "Acronym": "lec"}, {"Requester": "<PERSON>", "Acronym": "dib"}]}