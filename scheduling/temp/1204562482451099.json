{"df_asana": [{"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "464436269489794", "MaskList": "AAA", "ProjectMaskList": "05-46 EIGER 2.10", "LotToConfirmList": "5AADF101M00,test", "LotToSplitList": "4AACB201MX2", "PROJECTS": "MAGNIFY-RUN 1", "LotToManageList": "5AADF101M00,test"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "467496932920604", "MaskList": "AAB", "ProjectMaskList": "LER DEV", "LotToConfirmList": "6AADE101M00,2235_29274", "LotToSplitList": "6AADD201M00", "PROJECTS": "2nd Metal Layer - Shortloop", "LotToManageList": "5AACH101ILA,0586_240725270"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "489733785249552", "MaskList": "AAC", "ProjectMaskList": "05-50 LER2", "LotToConfirmList": null, "LotToSplitList": "6AABS105M00", "PROJECTS": "MERLIN - RERUN3", "LotToManageList": "6AADE101M00,2235_29274"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "513842320205279", "MaskList": "AAD", "ProjectMaskList": "05-54 EIGER 2.11 UCUT", "LotToConfirmList": null, "LotToSplitList": "6AABS104M00", "PROJECTS": "MERLIN - RERUN2", "LotToManageList": "4AACB201MX2,MAGNIFY-RUN 1"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "794540907829992", "MaskList": "AAE", "ProjectMaskList": "05-56 MPW31 ILDB", "LotToConfirmList": null, "LotToSplitList": "6AABS103M00", "PROJECTS": "MERLIN - RERUN", "LotToManageList": "6AADD201M00,2nd Metal Layer - Shortloop"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "869201729673462", "MaskList": "AAF", "ProjectMaskList": "MPW 29 3rd run", "LotToConfirmList": null, "LotToSplitList": "5AADC201M00", "PROJECTS": "MPW-LN-02", "LotToManageList": "6AABS105M00,MERLIN - RERUN3"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "669147563988443", "MaskList": "AAG", "ProjectMaskList": "05-55 UCSD", "LotToConfirmList": null, "LotToSplitList": "5AADA201M00", "PROJECTS": "photonixFAB_WP8_01", "LotToManageList": "6AABS104M00,MERLIN - RERUN2"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1131443696074648", "MaskList": "AAH", "ProjectMaskList": "05-61_LIGH", "LotToConfirmList": null, "LotToSplitList": "6AACZ102M00", "PROJECTS": "E2.23 HAD2", "LotToManageList": "6AABS103M00,MERLIN - R<PERSON><PERSON>"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1125291682985446", "MaskList": "AAI", "ProjectMaskList": "05-60_PAKH", "LotToConfirmList": null, "LotToSplitList": "6AACZ101M00", "PROJECTS": "E2.23 HAD2", "LotToManageList": "5AADC201M00,MPW-LN-02"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1160066995528264", "MaskList": "AAL", "ProjectMaskList": "MPW32AB", "LotToConfirmList": null, "LotToSplitList": "6AACY101M00", "PROJECTS": "EIGER LSP4", "LotToManageList": "5AADA201M00,photonixFAB_WP8_01"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1171139149669535", "MaskList": "AAM", "ProjectMaskList": "E2.14 TEL", "LotToConfirmList": null, "LotToSplitList": "6AACX101M00", "PROJECTS": "E2.15 Wolverine3 TRIM.", "LotToManageList": "6AACZ102M00,E2.23 HAD2"}, {"requester_names_list": "Ana Catarina <PERSON>", "requester_gid_list": "1183107046965382", "MaskList": "AAN", "ProjectMaskList": "EIGER 2.16", "LotToConfirmList": null, "LotToSplitList": "6AACW201M00", "PROJECTS": "ETV6p2", "LotToManageList": "6AACZ101M00,E2.23 HAD2"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1191400555194352", "MaskList": "AAO", "ProjectMaskList": "1534_24025", "LotToConfirmList": null, "LotToSplitList": "6AACV203M00", "PROJECTS": "E2.12 SCTR1.c LOT3", "LotToManageList": "6AACY101M00,EIGER LSP4"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1193626179934205", "MaskList": "AAR", "ProjectMaskList": "<PERSON><PERSON><PERSON><PERSON>", "LotToConfirmList": null, "LotToSplitList": "6AACV202M00", "PROJECTS": "E2.12 SCTR1.c LOT2", "LotToManageList": "6AACX101M00,E2.15 Wolverine3 TRIM."}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199110939357003", "MaskList": "AAT", "ProjectMaskList": "MPW33AB", "LotToConfirmList": null, "LotToSplitList": "6AACV201M00", "PROJECTS": "E2.12 SCTR1.c LOT1", "LotToManageList": "6AACW201M00,ETV6p2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1199394325037760", "MaskList": "AAW", "ProjectMaskList": "Glider 3.1", "LotToConfirmList": null, "LotToSplitList": "5AACU101M00", "PROJECTS": "MPW-AN800-34_2", "LotToManageList": "6AACV203M00,E2.12 SCTR1.c LOT3"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1199990271835482", "MaskList": "AAX", "ProjectMaskList": "LGT labs PD MTP", "LotToConfirmList": null, "LotToSplitList": "6AACT201M00", "PROJECTS": "EIG RIB DEV", "LotToManageList": "6AACV202M00,E2.12 SCTR1.c LOT2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1200295708067936", "MaskList": "AAY", "ProjectMaskList": "8.4 - LOT1", "LotToConfirmList": null, "LotToSplitList": "6AACR201M00", "PROJECTS": "PF_WP4_LOCA_S", "LotToManageList": "6AACV201M00,E2.12 SCTR1.c LOT1"}, {"requester_names_list": "<PERSON> Yao", "requester_gid_list": "1200692689203662", "MaskList": "ABB", "ProjectMaskList": "redesign EIGR6p4", "LotToConfirmList": null, "LotToSplitList": "5AACQ202M00", "PROJECTS": "PF_WP7/8_PD_02", "LotToManageList": "5AACU101M00,MPW-AN800-34_2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201328860961631", "MaskList": "ABC", "ProjectMaskList": "1668_16160", "LotToConfirmList": null, "LotToSplitList": "5AACQ201M00", "PROJECTS": "PF_WP7/8_PD_02", "LotToManageList": "6AACT201M00,EIG RIB DEV"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201466301932731", "MaskList": "ABD", "ProjectMaskList": "1037_21085_1", "LotToConfirmList": null, "LotToSplitList": "5AACM101M00", "PROJECTS": "QPIC Run 1", "LotToManageList": "6AACR201M00,PF_WP4_LOCA_S"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201752762206320", "MaskList": "ABE", "ProjectMaskList": "1037_21085_2", "LotToConfirmList": null, "LotToSplitList": "5AACL201M17", "PROJECTS": "PF_WP8_LNOI", "LotToManageList": "5AACQ202M00,PF_WP7/8_PD_02"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1201788320590322", "MaskList": "ABF", "ProjectMaskList": "E2.12 SCTR1.a", "LotToConfirmList": null, "LotToSplitList": "5AACH101M03", "PROJECTS": "0586_240725270", "LotToManageList": "5AACQ201M00,PF_WP7/8_PD_02"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201830090104290", "MaskList": "ABG", "ProjectMaskList": "E2.19a", "LotToConfirmList": null, "LotToSplitList": "6AACG101M13", "PROJECTS": "Dynamos2_an350", "LotToManageList": "5AACM101M00,QPIC Run 1"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1201899025933306", "MaskList": "ABJ", "ProjectMaskList": "E7.2 PDN2 LOT1", "LotToConfirmList": null, "LotToSplitList": "5AACF201M04", "PROJECTS": "EWOC-run1", "LotToManageList": "5AACA102M00,MPW-AN800-34"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201922419740872", "MaskList": "ABL", "ProjectMaskList": "E2.12 SCTR1 DOE LOT", "LotToConfirmList": null, "LotToSplitList": "5AACA101M03", "PROJECTS": "MPW-AN800-34", "LotToManageList": "5AACL201M17,PF_WP8_LNOI"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1202129345004455", "MaskList": "ABO", "ProjectMaskList": "E2.17 Wolverine3 TRIM.", "LotToConfirmList": null, "LotToSplitList": "6AABT201M09", "PROJECTS": "E6.8", "LotToManageList": "5AACH101M03,0586_240725270"}, {"requester_names_list": "Houssein EL DIRANI", "requester_gid_list": "1202135074352084", "MaskList": "ABP", "ProjectMaskList": "E2.18 Wolverine3 TRIM.", "LotToConfirmList": null, "LotToSplitList": "6AABP201M11", "PROJECTS": "E2.18 Wolverine3 TRIM.", "LotToManageList": "6AACG101M13,Dynamos2_an350"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202444549987515", "MaskList": "ABR", "ProjectMaskList": "E6.6", "LotToConfirmList": null, "LotToSplitList": "6AABO201M11", "PROJECTS": "E2.17 Wolverine3 TRIM.", "LotToManageList": "5AACF201M04,EWOC-run1"}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1202684057491700", "MaskList": "ABS", "ProjectMaskList": "MERLIN - RERUN3", "LotToConfirmList": null, "LotToSplitList": "6AABJ202M14", "PROJECTS": "E7.2 PDN2 LOT1", "LotToManageList": "5AACA101M03,MPW-AN800-34"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1202907572860628", "MaskList": "ABT", "ProjectMaskList": "E6.8", "LotToConfirmList": null, "LotToSplitList": "6AABJ201M14", "PROJECTS": "E7.2 PDN2 LOT2", "LotToManageList": "6AABT201M09,E6.8"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203079565264253", "MaskList": "ABU", "ProjectMaskList": "MPW-AN350-01B", "LotToConfirmList": null, "LotToSplitList": "6AAAB202M09", "PROJECTS": "LER DEV", "LotToManageList": "6AABP201M11,E2.18 Wolverine3 TRIM."}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1203283724869310", "MaskList": "ABV", "ProjectMaskList": "1955_240501133", "LotToConfirmList": null, "LotToSplitList": "6AAAM102M11", "PROJECTS": "E2.14 TEL", "LotToManageList": "6AABO201M11,E2.17 Wolverine3 TRIM."}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1203601372370011", "MaskList": "ABZ", "ProjectMaskList": "MPW-LN-01", "LotToConfirmList": null, "LotToSplitList": "5AAAW201M02", "PROJECTS": "Glider 3.1", "LotToManageList": "6AABJ202M14,E7.2 PDN2 LOT1"}, {"requester_names_list": "Audie <PERSON>", "requester_gid_list": "1203873691444950", "MaskList": "ACA", "ProjectMaskList": "MPW-AN800-34", "LotToConfirmList": null, "LotToSplitList": "6AAAM201M11", "PROJECTS": "E2.14", "LotToManageList": "6AABJ201M14,E7.2 PDN2 LOT2"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1203869571959179", "MaskList": "ACB", "ProjectMaskList": "MAGNIFY-RUN 1", "LotToConfirmList": null, "LotToSplitList": "5AABZ201M04", "PROJECTS": "MPW-LN-01", "LotToManageList": "6AAAB202M09,LER DEV"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204469536661025", "MaskList": "ACF", "ProjectMaskList": "EWOC-run1", "LotToConfirmList": null, "LotToSplitList": "4AACB201M19", "PROJECTS": "MAGNIFY-RUN 1", "LotToManageList": "6AAAM102M11,E2.14 TEL"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204562481875818", "MaskList": "ACG", "ProjectMaskList": "Dynamos2_an350", "LotToConfirmList": null, "LotToSplitList": "5AABV101M01", "PROJECTS": "1955_240501133", "LotToManageList": "5AAAW201M02,Glider 3.1"}, {"requester_names_list": "Moustapha JAFFAL", "requester_gid_list": "1204562482451099", "MaskList": "ACH", "ProjectMaskList": "0586_240725270", "LotToConfirmList": null, "LotToSplitList": "6AABU101M11", "PROJECTS": "MPW-AN350-01B", "LotToManageList": "6AAAM201M11,E2.14"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1204325331381742", "MaskList": "ACL", "ProjectMaskList": "PF_WP8_LNOI", "LotToConfirmList": null, "LotToSplitList": "6AABF104M08", "PROJECTS": "E2.12 SCTR1.a", "LotToManageList": "5AABZ201M04,MPW-LN-01"}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1204701988903544", "MaskList": "ACM", "ProjectMaskList": "QPIC Run 1", "LotToConfirmList": null, "LotToSplitList": "6AABL201M09", "PROJECTS": "E2.12 SCTR1 DOE LOT", "LotToManageList": "4AACB201M19,MAGNIFY-RUN 1"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204950886823380", "MaskList": "ACQ", "ProjectMaskList": "PF_WP7/8_PD_02", "LotToConfirmList": null, "LotToSplitList": "6AABL202M00", "PROJECTS": "E2.12 SCTR1.b", "LotToManageList": "5AABV101M01,1955_240501133"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1204968822616977", "MaskList": "ACR", "ProjectMaskList": "PF_WP4_LOCA_S", "LotToConfirmList": null, "LotToSplitList": "6AAAN101M14", "PROJECTS": "EIGER 2.16", "LotToManageList": "6AAAN102M00,EIGER 2.16"}, {"requester_names_list": "Viphretuo Mere", "requester_gid_list": "1204971721140670", "MaskList": "ACT", "ProjectMaskList": "EIG RIB DEV", "LotToConfirmList": null, "LotToSplitList": "5AAAR101M00", "PROJECTS": "<PERSON><PERSON><PERSON><PERSON>", "LotToManageList": "6AABU101M11,MPW-AN350-01B"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1205085908688191", "MaskList": "ACU", "ProjectMaskList": "MPW-AN800-34_2", "LotToConfirmList": null, "LotToSplitList": "6AABR201M09", "PROJECTS": "E6.6", "LotToManageList": "6AABU102M10,MPW-AN350-01A"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205137449747223", "MaskList": "ACV", "ProjectMaskList": "E2.12 SCTR1.c LOT3", "LotToConfirmList": null, "LotToSplitList": "6AABG201M09", "PROJECTS": "E2.19a", "LotToManageList": "6AABF104M08,E2.12 SCTR1.a"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1201278474361148", "MaskList": "ACW", "ProjectMaskList": "ETV6p2", "LotToConfirmList": null, "LotToSplitList": "6AABF103M08", "PROJECTS": "E2.13b", "LotToManageList": "6AABL201M09,E2.12 SCTR1 DOE LOT"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205595234938764", "MaskList": "ACX", "ProjectMaskList": "E2.15 Wolverine3 TRIM.", "LotToConfirmList": null, "LotToSplitList": "5AABD101M02", "PROJECTS": "1037_21085_1", "LotToManageList": "6AABL202M00,E2.12 SCTR1.b"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205709973073733", "MaskList": "ACY", "ProjectMaskList": "EIGER LSP4", "LotToConfirmList": null, "LotToSplitList": "5AAAF103M03", "PROJECTS": "MPW 29 3rd run", "LotToManageList": "6AAAN101M14,<PERSON>IG<PERSON> 2.16"}, {"requester_names_list": "Yann K<PERSON>ZY<PERSON>", "requester_gid_list": "1204164213558726", "MaskList": "ACZ", "ProjectMaskList": "E2.23 HAD2", "LotToConfirmList": null, "LotToSplitList": "5AABE101M02", "PROJECTS": "1037_21085_2", "LotToManageList": "5AAAR101M00,<PERSON>ermat<PERSON>"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1205847382524398", "MaskList": "ADA", "ProjectMaskList": "photonixFAB_WP8_01", "LotToConfirmList": null, "LotToSplitList": "5AAAT101M03", "PROJECTS": "MPW33AB", "LotToManageList": "6AABR201M09,E6.6"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1205874105867614", "MaskList": "ADC", "ProjectMaskList": "MPW-LN-02", "LotToConfirmList": null, "LotToSplitList": "5AAAL101M03", "PROJECTS": "MPW32AB", "LotToManageList": "6AABG201M09,E2.19a"}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1206225376712932", "MaskList": "ADD", "ProjectMaskList": "2nd Metal Layer - Shortloop", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AABF103M08,E2.13b"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206468476801011", "MaskList": "ADE", "ProjectMaskList": "2235_29274", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AABD101M02,1037_21085_1"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477227688", "MaskList": "ADF", "ProjectMaskList": "test", "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AAAF10<PERSON><PERSON><PERSON>,MPW 29 3rd run"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206468477262721", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AAAF103M03,MP<PERSON> 29 3rd run"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206660149483972", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AABE101M02,1037_21085_2"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660407180541", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AAAT101M03,MPW33AB"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408294625", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AABG202M08,E2.19a for FA"}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1206660408413444", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "6AABG201X3B,E2.19a"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408470048", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": "5AAAL101M03,MPW32AB"}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1206660408540230", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207080700250290", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207080702899449", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON><PERSON>", "requester_gid_list": "1207245129912719", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1207368970469755", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON>", "requester_gid_list": "1207453515525652", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207686975432982", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON><PERSON>", "requester_gid_list": "1207823714901978", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207389223844698", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207933125736626", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<PERSON><PERSON>", "requester_gid_list": "1207933129699238", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}, {"requester_names_list": "<EMAIL>", "requester_gid_list": "1207933129886224", "MaskList": null, "ProjectMaskList": null, "LotToConfirmList": null, "LotToSplitList": null, "PROJECTS": null, "LotToManageList": null}], "capacity_data_frame": [{"Lot Type": "Prototype", "Number of Lots": 7.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN800", "Priority": "Low", "Production date in": "Monday", "Projects": "MPW", "Task delay Update min (d)": 6.0, "Task advance update max (d)": 6.0, "max Lot reservation (d)": 14.0}, {"Lot Type": "Engineering", "Number of Lots": 7.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 8.0, "Wafer number max": 25, "Technology": "AN350", "Priority": "Normal", "Production date in": "Tuesday", "Projects": "EIGER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Pre-production", "Number of Lots": 4.0, "Wafer number min": 1.0, "Number of Lots per 2 weeks": 4.0, "Wafer number max": 25, "Technology": "AN200", "Priority": "High", "Production date in": "Wednesday", "Projects": "GLIDER", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "Proto-Engineering", "Number of Lots": 4.0, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": "Thursday", "Projects": "PHOTONIXFAB", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": "PhotonixFAB", "Number of Lots": 10000.0, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": "Friday", "Projects": "MAGNIFY", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "DYNAMOS", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "QPIC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "FEMTOCHIP", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "Dedicated run AN800", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "<PERSON><PERSON>", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "PhotonHub", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "Dedicated run AN350", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "CSOC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": "                                                     ", "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "AN200 (engineering)", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "<PERSON><PERSON>", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}, {"Lot Type": NaN, "Number of Lots": NaN, "Wafer number min": NaN, "Number of Lots per 2 weeks": NaN, "Wafer number max": NaN, "Technology": NaN, "Priority": NaN, "Production date in": NaN, "Projects": "6G-EWOC", "Task delay Update min (d)": NaN, "Task advance update max (d)": NaN, "max Lot reservation (d)": NaN}], "OP_data_frame": [{"MaskPlatform": "AN800", "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 3.0, "CS (d)": 10.0, "Pre-production[days]": 7.0, "Backend (d)": 14.0, "Split (d)": 3.0, "Tech_PCM": "AN800", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1YNjfsOnVtwb51LfxdSmlaYo3Al7R8t2YXl2doYmPgdg/edit?gid=772083255#gid=772083255", "Platform": "AN800", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": "AN350", "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 14.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": "AN350", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1RHS_Gx6yG2D97pXFDUPWLo07vxudEYUvOXU3OuS-mSs/edit#gid=45736496", "Platform": "AN800", "Module name": "CORE", "Names to be used in Talos selection": "CORE (X1)", "old Mask Set[d]": 22, "old Rerun[d]": 18, "New Mask Set[d]": 29, "Rerun[d]": 25, "Additional time [>10Wafers]": 5}, {"MaskPlatform": "AN200", "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": "AN200", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1tuzB1bX2zilyu5KX0brUwLKKFihsrsmrwU8qS3RlyMY/edit#gid=45736496", "Platform": "AN800", "Module name": "X2", "Names to be used in Talos selection": "X2", "old Mask Set[d]": 10, "old Rerun[d]": 7, "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "X3", "Names to be used in Talos selection": "X3", "old Mask Set[d]": 15, "old Rerun[d]": 11, "New Mask Set[d]": 15, "Rerun[d]": 11, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if X2,X3 selected)", "old Mask Set[d]": 5, "old Rerun[d]": 5, "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if X2,X3 selected)", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "old Mask Set[d]": 28, "old Rerun[d]": 19, "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "old Mask Set[d]": 10, "old Rerun[d]": 6, "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "RIB", "Names to be used in Talos selection": "RIB", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66643", "Names to be used in Talos selection": "FF66643 (small frame EIGER only)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN350", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223-1mm", "Names to be used in Talos selection": "FF66233-1mm  (new reduced frame EIGER only)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3", "Names to be used in Talos selection": "MX3 (core)", "old Mask Set[d]": 18, "old Rerun[d]": 15, "New Mask Set[d]": 20, "Rerun[d]": 17, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3X2", "Names to be used in Talos selection": "MX3X2", "old Mask Set[d]": 10, "old Rerun[d]": 7, "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": "        ", "Platform": "AN350", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if MX3X2 selected)", "old Mask Set[d]": 5, "old Rerun[d]": 5, "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "6G-EWOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if MX3X2 selected)", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "old Mask Set[d]": 28, "old Rerun[d]": 19, "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3RIB", "Names to be used in Talos selection": "MX3RIB (rib)", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 11, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "RESIST_OPEN", "Names to be used in Talos selection": "RESIST_OPEN", "old Mask Set[d]": 5, "old Rerun[d]": 5, "New Mask Set[d]": 5, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "old Mask Set[d]": 10, "old Rerun[d]": 6, "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA-S", "Names to be used in Talos selection": "LOCA-S ", "old Mask Set[d]": 20, "old Rerun[d]": 16, "New Mask Set[d]": 20, "Rerun[d]": 16, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "MX2", "Names to be used in Talos selection": "MX2 (core)", "old Mask Set[d]": 16, "old Rerun[d]": 12, "New Mask Set[d]": 18, "Rerun[d]": 14, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "ISOTRENCH", "Names to be used in Talos selection": "ISOTRENCH", "old Mask Set[d]": 15, "old Rerun[d]": 12, "New Mask Set[d]": 7, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}], "ENG_data_frame": [{"MaskPlatform": "AN800", "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": 10.0, "Lot project": "MPW", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": 1.0, "Shipment (d)": 3.0, "CS (d)": 10.0, "Pre-production[days]": 7.0, "Backend (d)": 14.0, "Split (d)": 3.0, "Tech_PCM": "AN800", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1YNjfsOnVtwb51LfxdSmlaYo3Al7R8t2YXl2doYmPgdg/edit?gid=772083255#gid=772083255", "Platform": "AN800", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": "AN350", "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 14.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "EIGER", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": "AN350", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1RHS_Gx6yG2D97pXFDUPWLo07vxudEYUvOXU3OuS-mSs/edit#gid=45736496", "Platform": "AN800", "Module name": "CORE", "Names to be used in Talos selection": "CORE (X1)", "old Mask Set[d]": 22, "old Rerun[d]": 18, "New Mask Set[d]": 29, "Rerun[d]": 25, "Additional time [>10Wafers]": 5}, {"MaskPlatform": "AN200", "Data Release(d)": 1.0, "Mask Release(d)": 10.0, "Mask available (d)": 7.0, "RFQ-PO (d)": 10.0, "Erfurt-PO (d)": NaN, "Lot project": "GLIDER", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": "AN200", "MetrologyPlan": "https://docs.google.com/spreadsheets/d/1tuzB1bX2zilyu5KX0brUwLKKFihsrsmrwU8qS3RlyMY/edit#gid=45736496", "Platform": "AN800", "Module name": "X2", "Names to be used in Talos selection": "X2", "old Mask Set[d]": 10, "old Rerun[d]": 7, "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PHOTONIXFAB", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "X3", "Names to be used in Talos selection": "X3", "old Mask Set[d]": 15, "old Rerun[d]": 11, "New Mask Set[d]": 15, "Rerun[d]": 11, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "MAGNIFY", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if X2,X3 selected)", "old Mask Set[d]": 5, "old Rerun[d]": 5, "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "DYNAMOS", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if X2,X3 selected)", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "QPIC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "old Mask Set[d]": 28, "old Rerun[d]": 19, "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "FEMTOCHIP", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "old Mask Set[d]": 10, "old Rerun[d]": 6, "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN800", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "RIB", "Names to be used in Talos selection": "RIB", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "PhotonHub", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN800", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66643", "Names to be used in Talos selection": "FF66643 (small frame EIGER only)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "Dedicated run AN350", "PCM": "PCM AN350", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "FF66223-1mm", "Names to be used in Talos selection": "FF66233-1mm  (new reduced frame EIGER only)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "CSOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3", "Names to be used in Talos selection": "MX3 (core)", "old Mask Set[d]": 18, "old Rerun[d]": 15, "New Mask Set[d]": 20, "Rerun[d]": 17, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "AN200 (engineering)", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3X2", "Names to be used in Talos selection": "MX3X2", "old Mask Set[d]": 10, "old Rerun[d]": 7, "New Mask Set[d]": 12, "Rerun[d]": 9, "Additional time [>10Wafers]": 5}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "<PERSON><PERSON>", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "ILDA", "Names to be used in Talos selection": "ILDA (1.7 um oxide on top of the core module, even if MX3X2 selected)", "old Mask Set[d]": 5, "old Rerun[d]": 5, "New Mask Set[d]": 6, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": "6G-EWOC", "PCM": "PCM AN800", "Testing/wafer (h)": 2.0, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "ILDB", "Names to be used in Talos selection": "ILDB (6.6 um oxide on top of the core module, even if MX3X2 selected)", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 15, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "HEATER", "Names to be used in Talos selection": "HEATER (starts from P1+)", "old Mask Set[d]": 28, "old Rerun[d]": 19, "New Mask Set[d]": 30, "Rerun[d]": 22, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "MX3RIB", "Names to be used in Talos selection": "MX3RIB (rib)", "old Mask Set[d]": 15, "old Rerun[d]": 15, "New Mask Set[d]": 15, "Rerun[d]": 11, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "RESIST_OPEN", "Names to be used in Talos selection": "RESIST_OPEN", "old Mask Set[d]": 5, "old Rerun[d]": 5, "New Mask Set[d]": 5, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA", "Names to be used in Talos selection": "LOCA (Loca-i)", "old Mask Set[d]": 10, "old Rerun[d]": 6, "New Mask Set[d]": 10, "Rerun[d]": 6, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "LOCA-S", "Names to be used in Talos selection": "LOCA-S ", "old Mask Set[d]": 20, "old Rerun[d]": 16, "New Mask Set[d]": 20, "Rerun[d]": 16, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CS", "Names to be used in Talos selection": "CS (AFD / TCD module)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "CSPLUS", "Names to be used in Talos selection": "CSPLUS (CS module to be used in case UCUT is selected)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN350", "Module name": "UCUT", "Names to be used in Talos selection": "UCUT (undercut trenches)", "old Mask Set[d]": 10, "old Rerun[d]": 10, "New Mask Set[d]": 10, "Rerun[d]": 10, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "FF66223", "Names to be used in Talos selection": "FF66233 (classic frozen frame)", "old Mask Set[d]": 0, "old Rerun[d]": 0, "New Mask Set[d]": 0, "Rerun[d]": 0, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "MX2", "Names to be used in Talos selection": "MX2 (core)", "old Mask Set[d]": 16, "old Rerun[d]": 12, "New Mask Set[d]": 18, "Rerun[d]": 14, "Additional time [>10Wafers]": 0}, {"MaskPlatform": NaN, "Data Release(d)": NaN, "Mask Release(d)": NaN, "Mask available (d)": NaN, "RFQ-PO (d)": NaN, "Erfurt-PO (d)": NaN, "Lot project": NaN, "PCM": NaN, "Testing/wafer (h)": NaN, "Data processing/wafer (d)": NaN, "Shipment (d)": NaN, "CS (d)": NaN, "Pre-production[days]": NaN, "Backend (d)": NaN, "Split (d)": NaN, "Tech_PCM": NaN, "MetrologyPlan": NaN, "Platform": "AN200", "Module name": "ISOTRENCH", "Names to be used in Talos selection": "ISOTRENCH", "old Mask Set[d]": 15, "old Rerun[d]": 12, "New Mask Set[d]": 7, "Rerun[d]": 5, "Additional time [>10Wafers]": 0}], "AsanaProject_data_frame": [{"Asana project": "Mask assembly", "project gid": 1203927479554343.0, "Portfolio": "Prototype", "Portfolio gid": 1206397258164834.0, "Talos version": "1.0.0"}, {"Asana project": "RFQ-PO", "project gid": 1206397258493014.0, "Portfolio": "Engineering", "Portfolio gid": 1206397258164837.0, "Talos version": NaN}, {"Asana project": "PI", "project gid": 1206397258492997.0, "Portfolio": "PhotonixFAB", "Portfolio gid": 1207525923277412.0, "Talos version": NaN}, {"Asana project": "Testing", "project gid": 1206397258164847.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Data processing", "project gid": 1206397258493001.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Shipment", "project gid": 1206397258493005.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "CS", "project gid": 1206521529181046.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Backend", "project gid": 1206110145947517.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "TestingCH", "project gid": 1202032814431764.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": "Grinding", "project gid": 1206521529181050.0, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": NaN}, {"Asana project": NaN, "project gid": NaN, "Portfolio": NaN, "Portfolio gid": NaN, "Talos version": ","}], "LotNumber_data_frame": [{"Technology": "AN800", "Technology code": "5A", "Lot Type": "Pre-production", "Lot Type code": 0.0, "Modules": "X1+Heater", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "CORE", "Layer code": "X1B", "Layer Name": "X1 bonding", "Unnamed: 12": NaN, "Technology (2digits)": "LGT code", "Unnamed: 14": "Technology", "Mask Number (3 digits)": "LGT Code ", "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": "XFAB Mask ref", "Lot type (1 digit)": "LGT code", "Unnamed: 20": "Lot type", "Run Number (2 digits)": "LGT Code ", "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": "LGT code", "Unnamed: 25": NaN, "Unnamed: 26": "Process step"}, {"Technology": "AN350", "Technology code": "6A", "Lot Type": "Prototype", "Lot Type code": 1.0, "Modules": "X1+Heater", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "X2", "Layer code": "X2B", "Layer Name": "X2 bonding", "Unnamed: 12": NaN, "Technology (2digits)": "5A", "Unnamed: 14": "AN800", "Mask Number (3 digits)": "A", "Unnamed: 16": "A", "Unnamed: 17": "A", "Unnamed: 18": NaN, "Lot type (1 digit)": 0, "Unnamed: 20": "Pre-production", "Run Number (2 digits)": 0, "Unnamed: 22": 0.0, "Unnamed: 23": 0.0, "Process step (2digits)": 0, "Unnamed: 25": 0.0, "Unnamed: 26": "End"}, {"Technology": "AN200", "Technology code": "4A", "Lot Type": "Engineering", "Lot Type code": 2.0, "Modules": "X1+ILDB", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "X3 ", "Layer code": "X3B", "Layer Name": "X3 bonding", "Unnamed: 12": NaN, "Technology (2digits)": "6A", "Unnamed: 14": "AN350", "Mask Number (3 digits)": 0, "Unnamed: 16": 0, "Unnamed: 17": 1, "Unnamed: 18": NaN, "Lot type (1 digit)": 1, "Unnamed: 20": "Prototype", "Run Number (2 digits)": 0, "Unnamed: 22": 0.0, "Unnamed: 23": 1.0, "Process step (2digits)": 0, "Unnamed: 25": 1.0, "Unnamed: 26": "X1"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": "Production", "Lot Type code": 3.0, "Modules": "X1+ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "ILDA", "Layer code": "ILA", "Layer Name": "ILDA", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": 0, "Unnamed: 16": 0, "Unnamed: 17": 2, "Unnamed: 18": NaN, "Lot type (1 digit)": 2, "Unnamed: 20": "Engineering", "Run Number (2 digits)": 0, "Unnamed: 22": 0.0, "Unnamed: 23": 2.0, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": "X2"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "ILDB", "Layer code": "ILB", "Layer Name": "ILDB", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": 3, "Unnamed: 20": "Production", "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": "HEATER"}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "HEATER", "Layer code": "HET", "Layer Name": "HEATER", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "X2 bonding", "Layer_code": "X2B", "Layer state": 100, "Platform": "AN800", "Module name": "LOCA", "Layer code": "LCI", "Layer Name": "LOCA-i", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN800", "Module name": "RIB", "Layer code": "RIB", "Layer Name": "RIB bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN800", "Module name": "UCUT", "Layer code": "UCT", "Layer Name": "UCUT", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "HEATER", "Layer_code": "HET", "Layer state": 100, "Platform": "AN350", "Module name": "MX3", "Layer code": "MX3", "Layer Name": "MX3 bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "X1 bonding", "Layer_code": "X1B", "Layer state": 100, "Platform": "AN350", "Module name": "MX3X2", "Layer code": "X2B", "Layer Name": "MX3X2 bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": "Example", "Mask Number (3 digits)": "05-044-1-00-2", "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN350", "Module name": "ILDA", "Layer code": "ILA", "Layer Name": "ILDA", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "X1 RIB bonding", "Layer_code": "1RB", "Layer state": 100, "Platform": "AN350", "Module name": "ILDB", "Layer code": "ILB", "Layer Name": "ILDB", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN350", "Module name": "HEATER", "Layer code": "HET", "Layer Name": "HEATER", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN350", "Module name": "MX3RIB", "Layer code": "RIB", "Layer Name": "RIB bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN350", "Module name": "LOCA", "Layer code": "LCI", "Layer Name": "LOCA-i", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN350", "Module name": "LOCA-S", "Layer code": "LCS", "Layer Name": "LOCA-s", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": "AN350", "Module name": "UCUT", "Layer code": "UCT", "Layer Name": "UCUT", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "X3 bonding", "Layer_code": "X3B", "Layer state": 100, "Platform": "AN200", "Module name": "MX2", "Layer code": "MX2", "Layer Name": "MX2 bonding", "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "X3 RIB bonding", "Layer_code": "3RB", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "X3 RIB bonding", "Layer_code": "3RB", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "X3 RIB bonding", "Layer_code": "3RB", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "ILDA", "Layer_code": "ILA", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+Heater", "Layer": "END", "Layer_code": "M00", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+ILDB", "Layer": "END", "Layer_code": "M01", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater", "Layer": "END", "Layer_code": "M02", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+Heater+LOCA-i", "Layer": "END", "Layer_code": "M03", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3 bonding", "Layer": "END", "Layer_code": "M04", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X3+Heater", "Layer": "END", "Layer_code": "M05", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+ILDB", "Layer": "END", "Layer_code": "M06", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+ILDB", "Layer": "END", "Layer_code": "M07", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 Bonding ", "Layer": "END", "Layer_code": "M08", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDA", "Layer": "END", "Layer_code": "M09", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + ILDB", "Layer": "END", "Layer_code": "M10", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + HEATER", "Layer": "END", "Layer_code": "M11", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDA", "Layer": "END", "Layer_code": "M12", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB + ILDB", "Layer": "END", "Layer_code": "M13", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX3 + RIB +HEATER", "Layer": "END", "Layer_code": "M14", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater+LOCA-i", "Layer": "END", "Layer_code": "M15", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+RIB+Heater", "Layer": "END", "Layer_code": "M16", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1 bonding", "Layer": "END", "Layer_code": "M17", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "SiN dep", "Layer": "END", "Layer_code": "M18", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "MX2 Bonding", "Layer": "END", "Layer_code": "M19", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X1+X2+LOCA-i", "Layer": "END", "Layer_code": "M20", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}, {"Technology": NaN, "Technology code": NaN, "Lot Type": NaN, "Lot Type code": NaN, "Modules": "X3+X2+LOCA-S", "Layer": "END", "Layer_code": "M21", "Layer state": 100, "Platform": NaN, "Module name": NaN, "Layer code": NaN, "Layer Name": NaN, "Unnamed: 12": NaN, "Technology (2digits)": NaN, "Unnamed: 14": NaN, "Mask Number (3 digits)": NaN, "Unnamed: 16": NaN, "Unnamed: 17": NaN, "Unnamed: 18": NaN, "Lot type (1 digit)": NaN, "Unnamed: 20": NaN, "Run Number (2 digits)": NaN, "Unnamed: 22": NaN, "Unnamed: 23": NaN, "Process step (2digits)": NaN, "Unnamed: 25": NaN, "Unnamed: 26": NaN}], "Email_frame": [{"Requester": "<PERSON><PERSON>", "Acronym": "dav"}, {"Requester": "<PERSON>", "Acronym": "maz"}, {"Requester": "<PERSON>", "Acronym": "mwg"}, {"Requester": "<PERSON><PERSON>", "Acronym": "gbn"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "kal"}, {"Requester": "<PERSON>", "Acronym": "tat"}, {"Requester": "<PERSON>", "Acronym": "ast"}, {"Requester": "<PERSON><PERSON>", "Acronym": "fin"}, {"Requester": "<PERSON>", "Acronym": "tom"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "arc"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mar"}, {"Requester": "Ana Catarina <PERSON>", "Acronym": "acm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "ozp"}, {"Requester": "<PERSON>", "Acronym": "hry"}, {"Requester": "<PERSON>", "Acronym": "anb"}, {"Requester": "<PERSON>", "Acronym": "anf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "jeg"}, {"Requester": "<PERSON>", "Acronym": "jfc"}, {"Requester": "<PERSON> Yao", "Acronym": "cay"}, {"Requester": "<PERSON>", "Acronym": "piw"}, {"Requester": "<PERSON><PERSON>", "Acronym": "cob"}, {"Requester": "<PERSON>", "Acronym": "jdf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "plz"}, {"Requester": "<PERSON>", "Acronym": "sab"}, {"Requester": "<PERSON>", "Acronym": "stm"}, {"Requester": "<PERSON><PERSON>", "Acronym": "asa"}, {"Requester": "<PERSON>", "Acronym": "axp"}, {"Requester": "<PERSON>", "Acronym": "gug"}, {"Requester": "Houssein EL DIRANI", "Acronym": "hod"}, {"Requester": "<PERSON><PERSON>", "Acronym": "dab"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON>", "Acronym": "abh"}, {"Requester": "<PERSON><PERSON>", "Acronym": "lde"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "alf"}, {"Requester": "<PERSON>", "Acronym": "cls"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "tol"}, {"Requester": "Audie <PERSON>", "Acronym": "aye"}, {"Requester": "<PERSON><PERSON>", "Acronym": "mat"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "stc"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "git"}, {"Requester": "Moustapha JAFFAL", "Acronym": "moj"}, {"Requester": "<PERSON>", "Acronym": "anv"}, {"Requester": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Acronym": "abr"}, {"Requester": "Ulysse Fanda", "Acronym": "ulf"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "chr"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "khk"}, {"Requester": "Viphretuo Mere", "Acronym": "vim"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "zba"}, {"Requester": "<PERSON>", "Acronym": "fov"}, {"Requester": "<PERSON><PERSON>", "Acronym": "jod"}, {"Requester": "<PERSON>", "Acronym": "frg"}, {"Requester": "<PERSON>", "Acronym": "joj"}, {"Requester": "Yann K<PERSON>ZY<PERSON>", "Acronym": "yak"}, {"Requester": "<PERSON>", "Acronym": "gab"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "swp"}, {"Requester": "<PERSON><PERSON>", "Acronym": "yil"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "sds"}, {"Requester": "<PERSON>", "Acronym": "mab"}, {"Requester": "<PERSON>", "Acronym": "ann"}, {"Requester": "<PERSON><PERSON><PERSON>", "Acronym": "myl"}, {"Requester": "<PERSON>", "Acronym": "boz"}, {"Requester": "<PERSON>", "Acronym": "alb"}, {"Requester": "<EMAIL>", "Acronym": "adi"}, {"Requester": "<PERSON>", "Acronym": "lec"}, {"Requester": "<PERSON>", "Acronym": "dib"}], "project_data_frame": [{"list_projects_names": "5AAAE101M02,05-56 MPW31 Heater, start : 2024-02-21 , 126 days", "list_Start_ON": "2024-02-21", "list_Due_ON": "2024-06-26", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "126", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-02-21", "PIout": "2024-04-16", "PrePin": "", "PrePout": "", "Testout": "2024-04-23", "DataProcessOut": "2024-04-23", "ShipmentOut": "2024-04-29", "CSout": "", "BackendOut": "2024-06-26", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AAAE101M02,05-56 MPW31 Heater", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "4AAAH101M19,05-61_LIGH, start : 2024-03-08 , 131 days", "list_Start_ON": "2024-03-08", "list_Due_ON": "2024-07-17", "list_corridor": "Prototype", "list_tech": "AN200", "list_cycleTime": "131", "RFQPOin": "2024-03-11", "RFQPOout": "2024-03-21", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-03-08", "TOout": "2024-03-29", "PIin": "2024-03-29", "PIout": "2024-04-30", "PrePin": "", "PrePout": "", "Testout": "2024-05-06", "DataProcessOut": "2024-04-22", "ShipmentOut": "2024-05-08", "CSout": "", "BackendOut": "2024-07-17", "list_requester": "<PERSON>", "WaferNumberList": "15", "list_title": "4AAAH101M19,05-61_LIGH", "list_modules": "Proto lot : FF66223 , MX2", "list_project": "Dedicated run AN350"}, {"list_projects_names": "6AAAI001M08,05-60_PAKH, start : 2024-03-18 , 134 days", "list_Start_ON": "2024-03-18", "list_Due_ON": "2024-07-30", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "134", "RFQPOin": "2024-03-21", "RFQPOout": "2024-03-21", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-03-18", "TOout": "2024-03-21", "PIin": "2024-04-04", "PIout": "2024-05-30", "PrePin": "", "PrePout": "", "Testout": "2024-05-31", "DataProcessOut": "2024-05-31", "ShipmentOut": "2024-06-18", "CSout": "", "BackendOut": "2024-07-30", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "6AAAI001M08,05-60_PAKH", "list_modules": "Proto lot : FF66223 , MX3 , <PERSON>", "list_project": "PhotonHub"}, {"list_projects_names": "5AAAL101M03,MPW32AB, start : 2024-04-02 , 135 days", "list_Start_ON": "2024-04-02", "list_Due_ON": "2024-08-15", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "135", "RFQPOin": "2024-04-04", "RFQPOout": "2024-04-08", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-04-02", "TOout": "2024-04-19", "PIin": "2024-04-22", "PIout": "2024-07-12", "PrePin": "", "PrePout": "", "Testout": "2024-07-18", "DataProcessOut": "2024-07-18", "ShipmentOut": "2024-07-23", "CSout": "", "BackendOut": "2024-08-15", "list_requester": "<PERSON>", "WaferNumberList": "5", "list_title": "5AAAL101M03,MPW32AB", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "6AAAN101M14,EIGER 2.16, start : 2024-05-15 , 161 days", "list_Start_ON": "2024-05-15", "list_Due_ON": "2024-10-23", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "161", "RFQPOin": "2024-05-27", "RFQPOout": "2024-06-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-15", "TOout": "2024-06-14", "PIin": "2024-07-16", "PIout": "2024-10-01", "PrePin": "", "PrePout": "", "Testout": "2024-10-20", "DataProcessOut": "2024-10-21", "ShipmentOut": "2024-10-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "10", "list_title": "6AAAN101M14,<PERSON>IG<PERSON> 2.16", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AAAO001M10,1534_24025, start : 2024-05-22 , 87 days", "list_Start_ON": "2024-05-22", "list_Due_ON": "2024-08-17", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "87", "RFQPOin": "2024-05-20", "RFQPOout": "2024-05-30", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-22", "TOout": "2024-06-18", "PIin": "2024-06-17", "PIout": "2024-07-19", "PrePin": "2024-06-08", "PrePout": "2024-06-15", "Testout": "2024-07-22", "DataProcessOut": "2024-07-26", "ShipmentOut": "2024-08-03", "CSout": "", "BackendOut": "2024-08-17", "list_requester": "<PERSON> Yao", "WaferNumberList": "6", "list_title": "6AAAO001M10,1534_24025", "list_modules": "Proto lot : FF66223 , MX3 , ILDB , CS", "list_project": "Dedicated run AN350"}, {"list_projects_names": "5AABD101M02,1037_21085_1, start : 2024-05-06 , 150 days", "list_Start_ON": "2024-05-06", "list_Due_ON": "2024-10-03", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "150", "RFQPOin": "2024-05-06", "RFQPOout": "2024-05-16", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-06", "TOout": "2024-05-24", "PIin": "2024-05-29", "PIout": "2024-09-05", "PrePin": "", "PrePout": "", "Testout": "2024-09-06", "DataProcessOut": "2024-09-07", "ShipmentOut": "2024-09-09", "CSout": "", "BackendOut": "2024-10-03", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AABD101M02,1037_21085_1", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AABE101M02,1037_21085_2, start : 2024-05-29 , 156 days", "list_Start_ON": "2024-05-29", "list_Due_ON": "2024-11-01", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "156", "RFQPOin": "2024-05-30", "RFQPOout": "2024-06-09", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-29", "TOout": "2024-06-17", "PIin": "2024-06-25", "PIout": "2024-10-04", "PrePin": "", "PrePout": "", "Testout": "2024-10-05", "DataProcessOut": "2024-10-06", "ShipmentOut": "2024-10-08", "CSout": "", "BackendOut": "2024-11-01", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AABE101M02,1037_21085_2", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "Dedicated run AN800"}, {"list_projects_names": "6AAAM102M11,E2.14 TEL, start : 2024-09-09 , 54 days", "list_Start_ON": "2024-09-09", "list_Due_ON": "2024-11-02", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "54", "RFQPOin": "2024-05-06", "RFQPOout": "2024-05-16", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-09", "PIout": "2024-10-29", "PrePin": "", "PrePout": "", "Testout": "2024-10-30", "DataProcessOut": "2024-10-31", "ShipmentOut": "2024-11-02", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AAAM102M11,E2.14 TEL", "list_modules": "Proto lot : FF66223-1mm , MX3 , ILDA , HEATER , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AABF103M08,E2.13b, start : 2024-08-05 , 16 days", "list_Start_ON": "2024-08-05", "list_Due_ON": "2024-08-21", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "16", "RFQPOin": "2024-05-02", "RFQPOout": "2024-05-12", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-08-05", "PIout": "2024-08-15", "PrePin": "", "PrePout": "", "Testout": "2024-08-18", "DataProcessOut": "2024-08-19", "ShipmentOut": "2024-08-21", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABF103M08,E2.13b", "list_modules": "Proto lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AABF104M08,E2.12 SCTR1.a, start : 2024-07-18 , 55 days", "list_Start_ON": "2024-07-18", "list_Due_ON": "2024-09-11", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "55", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-07-18", "PIout": "2024-09-05", "PrePin": "", "PrePout": "", "Testout": "2024-09-08", "DataProcessOut": "2024-09-09", "ShipmentOut": "2024-09-11", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABF104M08,E2.12 SCTR1.a", "list_modules": "Proto lot : FF66223-1mm , MX3 , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AABU101M11,MPW-AN350-01B, start : 2024-06-03 , 147 days", "list_Start_ON": "2024-06-03", "list_Due_ON": "2024-10-28", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "147", "RFQPOin": "2024-06-03", "RFQPOout": "2024-06-13", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-03", "TOout": "2024-06-21", "PIin": "2024-07-17", "PIout": "2024-09-26", "PrePin": "", "PrePout": "", "Testout": "2024-10-01", "DataProcessOut": "2024-10-02", "ShipmentOut": "2024-10-04", "CSout": "", "BackendOut": "2024-10-28", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AABU101M11,MPW-AN350-01B", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "5AABV101M01,1955_240501133, start : 2024-07-05 , 92 days", "list_Start_ON": "2024-07-05", "list_Due_ON": "2024-10-05", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "92", "RFQPOin": "2024-06-27", "RFQPOout": "2024-07-08", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-05", "TOout": "2024-07-23", "PIin": "2024-07-23", "PIout": "2024-09-07", "PrePin": "", "PrePout": "", "Testout": "2024-09-08", "DataProcessOut": "2024-09-09", "ShipmentOut": "2024-09-11", "CSout": "", "BackendOut": "2024-10-05", "list_requester": "<PERSON> Yao", "WaferNumberList": "6", "list_title": "5AABV101M01,1955_240501133", "list_modules": "Proto lot : FF66223 , CORE , ILDB , CS", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AAAF103M03,MP<PERSON> 29 3rd run, start : 2024-06-06 , 111 days", "list_Start_ON": "2024-06-06", "list_Due_ON": "2024-09-25", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "111", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-06", "PIout": "2024-08-28", "PrePin": "", "PrePout": "", "Testout": "2024-08-29", "DataProcessOut": "2024-08-30", "ShipmentOut": "2024-09-01", "CSout": "", "BackendOut": "2024-09-25", "list_requester": "<PERSON>", "WaferNumberList": "2", "list_title": "5AAAF103M03,MP<PERSON> 29 3rd run", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "5AACA101M03,MPW-AN800-34, start : 2024-08-12 , 143 days", "list_Start_ON": "2024-08-12", "list_Due_ON": "2025-01-02", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "143", "RFQPOin": "2024-08-12", "RFQPOout": "2024-08-22", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-12", "TOout": "2024-08-30", "PIin": "2024-08-30", "PIout": "2024-12-05", "PrePin": "", "PrePout": "", "Testout": "2024-12-06", "DataProcessOut": "2024-12-07", "ShipmentOut": "2024-12-09", "CSout": "", "BackendOut": "2025-01-02", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "5AACA101M03,MPW-AN800-34", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "5AAAT102M01,MPW-AN800-33A, Splitted lot, start : 2024-06-03 , 76 days", "list_Start_ON": "2024-06-03", "list_Due_ON": "2024-08-18", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "76", "RFQPOin": "2024-06-03", "RFQPOout": "2024-06-13", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-03", "TOout": "2024-06-21", "PIin": "2024-06-20", "PIout": "2024-07-15", "PrePin": "", "PrePout": "", "Testout": "2024-07-25", "DataProcessOut": "2024-07-26", "ShipmentOut": "2024-07-28", "CSout": "", "BackendOut": "2024-08-18", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AAAT102M01,MPW-AN800-33A, Splitted lot", "list_modules": "Proto lot : FF66223 , CORE , ILDB", "list_project": "MPW"}, {"list_projects_names": "6AACG101M13,Dynamos2_an350, start : 2024-09-26 , 114 days", "list_Start_ON": "2024-09-26", "list_Due_ON": "2025-01-18", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "114", "RFQPOin": "2024-09-26", "RFQPOout": "2024-10-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-26", "TOout": "2024-10-21", "PIin": "2024-10-21", "PIout": "2024-12-20", "PrePin": "", "PrePout": "", "Testout": "2024-12-21", "DataProcessOut": "2024-12-22", "ShipmentOut": "2024-12-25", "CSout": "", "BackendOut": "2025-01-18", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "6", "list_title": "6AACG101M13,Dynamos2_an350", "list_modules": "Proto lot : FF66223 , MX3 , ILDB , MX3RIB , CS", "list_project": "DYNAMOS"}, {"list_projects_names": "5AACH101M03,0586_240725270, start : 2024-08-30 , 146 days", "list_Start_ON": "2024-08-30", "list_Due_ON": "2025-01-23", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "146", "RFQPOin": "2024-08-30", "RFQPOout": "2024-09-09", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-30", "TOout": "2024-09-17", "PIin": "2024-09-17", "PIout": "2024-12-26", "PrePin": "", "PrePout": "", "Testout": "2024-12-27", "DataProcessOut": "2024-12-28", "ShipmentOut": "2024-12-30", "CSout": "", "BackendOut": "2025-01-23", "list_requester": "<PERSON>", "WaferNumberList": "4", "list_title": "5AACH101M03,0586_240725270", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AACA102M00,MPW-AN800-34,Splitted lot, start : 2024-08-12 , 112 days", "list_Start_ON": "2024-08-12", "list_Due_ON": "2024-12-02", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "112", "RFQPOin": "2024-08-12", "RFQPOout": "2024-08-22", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-12", "TOout": "2024-08-30", "PIin": "2024-08-30", "PIout": "2024-11-04", "PrePin": "", "PrePout": "", "Testout": "2024-11-05", "DataProcessOut": "2024-11-06", "ShipmentOut": "2024-11-08", "CSout": "", "BackendOut": "2024-12-02", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AACA102M00,MPW-AN800-34,Splitted lot", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDB , CS", "list_project": "MPW"}, {"list_projects_names": "5AACM101M00,QPIC Run 1, start : 2024-10-17 , 85 days", "list_Start_ON": "2024-10-17", "list_Due_ON": "2025-01-10", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "85", "RFQPOin": "2024-10-17", "RFQPOout": "2024-10-27", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-10-17", "TOout": "2024-11-04", "PIin": "2024-11-04", "PIout": "2024-12-13", "PrePin": "", "PrePout": "", "Testout": "2024-12-14", "DataProcessOut": "2024-12-15", "ShipmentOut": "2024-12-17", "CSout": "", "BackendOut": "2025-01-10", "list_requester": "<PERSON><PERSON>", "WaferNumberList": "6", "list_title": "5AACM101M00,QPIC Run 1", "list_modules": "Proto lot : FF66223 , <PERSON><PERSON> , CS", "list_project": "QPIC"}, {"list_projects_names": "6AABR201MX3,E6.6,MX3 bonding, start : 2024-05-27 , 67 days", "list_Start_ON": "2024-05-27", "list_Due_ON": "2024-08-02", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "67", "RFQPOin": "2024-05-27", "RFQPOout": "2024-06-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-27", "TOout": "2024-06-14", "PIin": "2024-07-04", "PIout": "2024-07-25", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-08-01", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "22", "list_title": "6AABR201MX3,E6.6,MX3 bonding", "list_modules": "Eng lot : FF66643 , MX3 , ILDB", "list_project": "EIGER"}, {"list_projects_names": "5AAAF103X1B,MPW 29 3rd run,X1 bonding, start : 2024-06-06 , 34 days", "list_Start_ON": "2024-06-06", "list_Due_ON": "2024-07-10", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "34", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-06", "PIout": "2024-07-09", "PrePin": "", "PrePout": "", "Testout": "2024-07-10", "DataProcessOut": "2024-07-10", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "4", "list_title": "5AAAF103X1B,MPW 29 3rd run,X1 bonding", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "5AAAF103X2B,MPW 29 3rd run,X2 bonding, start : 2024-06-06 , 92 days", "list_Start_ON": "2024-06-06", "list_Due_ON": "2024-09-06", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "92", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-06", "PIout": "2024-09-04", "PrePin": "", "PrePout": "", "Testout": "2024-07-25", "DataProcessOut": "2024-09-06", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "4", "list_title": "5AAAF103X2B,MPW 29 3rd run,X2 bonding", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "5AAAF103HET,MPW 29 3rd run,HEATER, start : 2024-06-06 , 87 days", "list_Start_ON": "2024-06-06", "list_Due_ON": "2024-09-01", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "87", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-06-06", "PIout": "2024-08-28", "PrePin": "", "PrePout": "", "Testout": "2024-08-29", "DataProcessOut": "2024-08-30", "ShipmentOut": "2024-09-01", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "4", "list_title": "5AAAF103HET,MPW 29 3rd run,HEATER", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER", "list_project": "MPW"}, {"list_projects_names": "6AAAN102M00,EIGER 2.16,Splitted lot, start : 2024-05-15 , 118 days", "list_Start_ON": "2024-05-15", "list_Due_ON": "2024-09-10", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "118", "RFQPOin": "2024-05-27", "RFQPOout": "2024-06-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-15", "TOout": "2024-06-14", "PIin": "2024-08-25", "PIout": "2024-09-06", "PrePin": "", "PrePout": "", "Testout": "2024-09-07", "DataProcessOut": "2024-09-08", "ShipmentOut": "2024-09-10", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AAAN102M00,EIGER 2.16,Splitted lot", "list_modules": "Proto lot : FF66223 , MX3 , ILDB , MX3RIB", "list_project": "EIGER"}, {"list_projects_names": "5AADF101M00,test, start : 2028-03-12 , 129 days", "list_Start_ON": "2028-03-12", "list_Due_ON": "2028-07-19", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "129", "RFQPOin": "2028-03-12", "RFQPOout": "2028-03-22", "ERFPOin": "2028-06-02", "ERFPOout": "2028-06-12", "TOin": "2028-03-12", "TOout": "2028-03-30", "PIin": "2028-03-30", "PIout": "2028-06-08", "PrePin": "", "PrePout": "", "Testout": "2028-06-11", "DataProcessOut": "2028-06-12", "ShipmentOut": "2028-06-15", "CSout": "2028-07-05", "BackendOut": "2028-07-19", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AADF101M00,test", "list_modules": "Proto lot : FF66223 , CORE , ILDA , HEATER , CSPLUS , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AAAD201M11,05-54 EIGER 2.11, start : 2023-12-12 , 99 days", "list_Start_ON": "2023-12-12", "list_Due_ON": "2024-03-20", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "99", "RFQPOin": "2023-12-15", "RFQPOout": "2023-12-25", "ERFPOin": "", "ERFPOout": "", "TOin": "2023-12-12", "TOout": "2024-01-02", "PIin": "2024-01-02", "PIout": "2024-03-18", "PrePin": "", "PrePout": "", "Testout": "2024-03-18", "DataProcessOut": "2024-03-18", "ShipmentOut": "2024-03-20", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "2", "list_title": "6AAAD201M11,05-54 EIGER 2.11", "list_modules": "Eng lot : FF66223 , MX3 , ILDA , HEATER", "list_project": "EIGER"}, {"list_projects_names": "6AAAM201M11,EIGER 2.14, start : 2024-08-15 , 67 days", "list_Start_ON": "2024-08-15", "list_Due_ON": "2024-10-21", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "67", "RFQPOin": "2024-07-04", "RFQPOout": "2024-07-08", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-15", "TOout": "2024-09-09", "PIin": "2024-09-09", "PIout": "2024-10-15", "PrePin": "", "PrePout": "", "Testout": "2024-10-18", "DataProcessOut": "2024-10-19", "ShipmentOut": "2024-10-21", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AAAM201M11,<PERSON>IG<PERSON> 2.14", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "5AAAR101M00,Zermatt, start : 2024-06-14 , 101 days", "list_Start_ON": "2024-06-14", "list_Due_ON": "2024-09-23", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "101", "RFQPOin": "2024-05-16", "RFQPOout": "2024-05-26", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-14", "TOout": "2024-07-02", "PIin": "2024-07-10", "PIout": "2024-09-19", "PrePin": "", "PrePout": "", "Testout": "2024-09-20", "DataProcessOut": "2024-09-21", "ShipmentOut": "2024-09-23", "CSout": "", "BackendOut": "", "list_requester": "<PERSON> Yao", "WaferNumberList": "13", "list_title": "5AAAR101M00,<PERSON>ermat<PERSON>", "list_modules": "Proto lot : FF66223 , CORE , ILDA , HEATER", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AAAT101M03,MPW33AB, start : 2024-06-03 , 117 days", "list_Start_ON": "2024-06-03", "list_Due_ON": "2024-09-28", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "117", "RFQPOin": "2024-06-03", "RFQPOout": "2024-06-13", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-03", "TOout": "2024-06-21", "PIin": "2024-06-20", "PIout": "2024-08-31", "PrePin": "", "PrePout": "", "Testout": "2024-09-01", "DataProcessOut": "2024-09-02", "ShipmentOut": "2024-09-04", "CSout": "", "BackendOut": "2024-09-28", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AAAT101M03,MPW33AB", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "MPW"}, {"list_projects_names": "5AAAW201M02,Glider 3.1, start : 2024-09-26 , 146 days", "list_Start_ON": "2024-09-26", "list_Due_ON": "2025-02-19", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "146", "RFQPOin": "2024-09-26", "RFQPOout": "2024-10-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-26", "TOout": "2024-10-14", "PIin": "2024-10-14", "PIout": "2025-01-20", "PrePin": "", "PrePout": "", "Testout": "2025-01-23", "DataProcessOut": "2025-01-24", "ShipmentOut": "2025-01-26", "CSout": "", "BackendOut": "2025-02-19", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AAAW201M02,Glider 3.1", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , CS", "list_project": "GLIDER"}, {"list_projects_names": "6AAAA201ILA,05-46 EIGER 2.10,ILDA, start : 2023-11-23 , 209 days", "list_Start_ON": "2023-11-23", "list_Due_ON": "2024-06-19", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "209", "RFQPOin": "2023-11-23", "RFQPOout": "2023-12-03", "ERFPOin": "", "ERFPOout": "", "TOin": "2023-11-23", "TOout": "2023-12-14", "PIin": "2023-12-14", "PIout": "2024-04-05", "PrePin": "", "PrePout": "", "Testout": "2024-05-11", "DataProcessOut": "2024-05-12", "ShipmentOut": "2024-05-14", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "8", "list_title": "6AAAA201ILA,05-46 EIGER 2.10,ILDA", "list_modules": "Eng lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB", "list_project": "EIGER"}, {"list_projects_names": "6AABG201M09,E2.19a, start : 2024-04-01 , 140 days", "list_Start_ON": "2024-04-01", "list_Due_ON": "2024-08-19", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "140", "RFQPOin": "2024-04-27", "RFQPOout": "2024-05-07", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-04-01", "TOout": "2024-04-08", "PIin": "2024-08-05", "PIout": "2024-08-15", "PrePin": "", "PrePout": "", "Testout": "2024-08-16", "DataProcessOut": "2024-08-17", "ShipmentOut": "2024-08-19", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AABG201M09,E2.19a", "list_modules": "Eng lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABG201X3B,E2.19a,X3 bonding, start : 2024-04-01 , 154 days", "list_Start_ON": "2024-04-01", "list_Due_ON": "2024-09-02", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "154", "RFQPOin": "2024-04-27", "RFQPOout": "2024-05-07", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-04-01", "TOout": "2024-04-08", "PIin": "2024-08-05", "PIout": "2024-08-30", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-09-02", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "19", "list_title": "6AABG201X3B,E2.19a,X3 bonding", "list_modules": "Eng lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AABG202M08,E2.19a for FA,Splitted lot, start : 2024-04-01 , 133 days", "list_Start_ON": "2024-04-01", "list_Due_ON": "2024-08-12", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "133", "RFQPOin": "2024-04-27", "RFQPOout": "2024-05-07", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-04-01", "TOout": "2024-04-08", "PIin": "2024-05-31", "PIout": "2024-08-09", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-08-12", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AABG202M08,E2.19a for FA,Splitted lot", "list_modules": "Eng lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AAAB202M09,LER DEV, start : 2024-10-07 , 34 days", "list_Start_ON": "2024-10-07", "list_Due_ON": "2024-11-10", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "34", "RFQPOin": "2024-04-18", "RFQPOout": "2024-04-28", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-10-07", "PIout": "2024-11-04", "PrePin": "", "PrePout": "", "Testout": "2024-11-07", "DataProcessOut": "2024-11-08", "ShipmentOut": "2024-11-10", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AAAB202M09,LER DEV", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABJ201M14,E7.2 PDN2 LOT2, start : 2024-09-12 , 118 days", "list_Start_ON": "2024-09-12", "list_Due_ON": "2025-01-08", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "118", "RFQPOin": "2024-09-12", "RFQPOout": "2024-09-22", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-12", "TOout": "2024-10-07", "PIin": "2024-10-07", "PIout": "2025-01-01", "PrePin": "", "PrePout": "", "Testout": "2025-01-04", "DataProcessOut": "2025-01-05", "ShipmentOut": "2025-01-08", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABJ201M14,E7.2 PDN2 LOT2", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AABJ202M14,E7.2 PDN2 LOT1, start : 2024-10-07 , 92 days", "list_Start_ON": "2024-10-07", "list_Due_ON": "2025-01-07", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "92", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-10-07", "PIout": "2025-01-01", "PrePin": "", "PrePout": "", "Testout": "2025-01-04", "DataProcessOut": "2025-01-05", "ShipmentOut": "2025-01-07", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABJ202M14,E7.2 PDN2 LOT1", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AABL201M09,E2.12 SCTR1 DOE LOT, start : 2024-06-03 , 91 days", "list_Start_ON": "2024-06-03", "list_Due_ON": "2024-09-02", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "91", "RFQPOin": "2024-06-13", "RFQPOout": "2024-06-23", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-03", "TOout": "2024-06-20", "PIin": "2024-07-08", "PIout": "2024-08-27", "PrePin": "", "PrePout": "", "Testout": "2024-08-30", "DataProcessOut": "2024-08-31", "ShipmentOut": "2024-09-02", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABL201M09,E2.12 SCTR1 DOE LOT", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AABO201M11,E2.17 Wolverine3 TRIM., start : 2025-01-10 , 68 days", "list_Start_ON": "2025-01-10", "list_Due_ON": "2025-03-19", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "68", "RFQPOin": "2025-01-10", "RFQPOout": "2025-01-20", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-01-10", "TOout": "2025-02-04", "PIin": "2025-02-04", "PIout": "2025-03-12", "PrePin": "", "PrePout": "", "Testout": "2025-03-15", "DataProcessOut": "2025-03-16", "ShipmentOut": "2025-03-19", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABO201M11,E2.17 Wolverine3 TRIM.", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AABP201M11,E2.18 Wolverine3 TRIM., start : 2025-01-10 , 68 days", "list_Start_ON": "2025-01-10", "list_Due_ON": "2025-03-19", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "68", "RFQPOin": "2025-01-10", "RFQPOout": "2025-01-20", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-01-10", "TOout": "2025-02-04", "PIin": "2025-02-04", "PIout": "2025-03-12", "PrePin": "", "PrePout": "", "Testout": "2025-03-15", "DataProcessOut": "2025-03-16", "ShipmentOut": "2025-03-19", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABP201M11,E2.18 Wolverine3 TRIM.", "list_modules": "Eng lot : FF66223-1mm , MX3 , ILDA , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AABR201M09,E6.6, start : 2024-05-27 , 80 days", "list_Start_ON": "2024-05-27", "list_Due_ON": "2024-08-15", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "80", "RFQPOin": "2024-05-27", "RFQPOout": "2024-06-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-27", "TOout": "2024-06-14", "PIin": "2024-07-04", "PIout": "2024-08-09", "PrePin": "", "PrePout": "", "Testout": "2024-08-12", "DataProcessOut": "2024-08-13", "ShipmentOut": "2024-08-15", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AABR201M09,E6.6", "list_modules": "Eng lot : FF66643 , MX3 , ILDB", "list_project": "EIGER"}, {"list_projects_names": "6AABS201M09,E 6.7, start : 2024-05-27 , 50 days", "list_Start_ON": "2024-05-27", "list_Due_ON": "2024-07-16", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "50", "RFQPOin": "2024-05-27", "RFQPOout": "2024-06-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-27", "TOout": "2024-06-14", "PIin": "2024-06-14", "PIout": "2024-07-16", "PrePin": "", "PrePout": "", "Testout": "2024-07-11", "DataProcessOut": "2024-07-12", "ShipmentOut": "2024-07-15", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AABS201M09,E 6.7", "list_modules": "Eng lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABT201M09,E6.8, start : 2025-01-20 , 63 days", "list_Start_ON": "2025-01-20", "list_Due_ON": "2025-03-24", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "63", "RFQPOin": "2025-01-20", "RFQPOout": "2025-01-30", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-01-20", "TOout": "2025-02-14", "PIin": "2025-02-14", "PIout": "2025-03-17", "PrePin": "", "PrePout": "", "Testout": "2025-03-20", "DataProcessOut": "2025-03-21", "ShipmentOut": "2025-03-24", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABT201M09,E6.8", "list_modules": "Eng lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "5AABZ201M04,MPW-LN-01, start : 2024-06-19 , 74 days", "list_Start_ON": "2024-06-19", "list_Due_ON": "2024-09-01", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "74", "RFQPOin": "2024-06-17", "RFQPOout": "2024-07-01", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-19", "TOout": "2024-07-09", "PIin": "2024-07-17", "PIout": "2024-08-28", "PrePin": "", "PrePout": "", "Testout": "2024-08-29", "DataProcessOut": "2024-08-30", "ShipmentOut": "2024-09-01", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON>", "WaferNumberList": "8", "list_title": "5AABZ201M04,MPW-LN-01", "list_modules": "Eng lot : FF66223 , CO<PERSON> , X3", "list_project": "Dedicated run AN800"}, {"list_projects_names": "4AACB201M19,MAGNIFY-RUN 1, start : 2024-07-26 , 18 days", "list_Start_ON": "2024-07-26", "list_Due_ON": "2024-08-13", "list_corridor": "Engineering", "list_tech": "AN200", "list_cycleTime": "18", "RFQPOin": "2024-06-30", "RFQPOout": "2024-07-02", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-26", "TOout": "2024-08-13", "PIin": "2024-07-16", "PIout": "2024-08-05", "PrePin": "", "PrePout": "", "Testout": "2024-08-06", "DataProcessOut": "2024-08-07", "ShipmentOut": "2024-08-09", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "12", "list_title": "4AACB201M19,MAGNIFY-RUN 1", "list_modules": "Eng lot : FF66223 , MX2", "list_project": "MAGNIFY"}, {"list_projects_names": "6AABU102M10,MPW-AN350-01A,Splitted lot, start : 2024-06-03 , 127 days", "list_Start_ON": "2024-06-03", "list_Due_ON": "2024-10-08", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "127", "RFQPOin": "2024-06-03", "RFQPOout": "2024-06-13", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-06-03", "TOout": "2024-06-21", "PIin": "2024-08-23", "PIout": "2024-09-06", "PrePin": "", "PrePout": "", "Testout": "2024-09-11", "DataProcessOut": "2024-09-12", "ShipmentOut": "2024-09-14", "CSout": "", "BackendOut": "2024-10-08", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "6AABU102M10,MPW-AN350-01A,Splitted lot", "list_modules": "Proto lot : FF66223 , MX3 , ILDB , CS", "list_project": "MPW"}, {"list_projects_names": "5AACF201M04,EWOC-run1, start : 2024-10-11 , 66 days", "list_Start_ON": "2024-10-11", "list_Due_ON": "2024-12-16", "list_corridor": "Prototype", "list_tech": "AN800", "list_cycleTime": "66", "RFQPOin": "2024-10-11", "RFQPOout": "2024-10-21", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-10-11", "TOout": "2024-10-29", "PIin": "2024-10-29", "PIout": "2024-12-12", "PrePin": "", "PrePout": "", "Testout": "2024-12-13", "DataProcessOut": "2024-12-14", "ShipmentOut": "2024-12-16", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON>", "WaferNumberList": "8", "list_title": "5AACF201M04,EWOC-run1", "list_modules": "Eng lot : FF66223 , CO<PERSON> , X3", "list_project": "6G-EWOC"}, {"list_projects_names": "6AABS202M00,E6.7 MX3 Bonding - 17 wafers, start : 2024-05-27 , 45 days", "list_Start_ON": "2024-05-27", "list_Due_ON": "2024-07-11", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "45", "RFQPOin": "2024-05-27", "RFQPOout": "2024-06-06", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-05-27", "TOout": "2024-06-14", "PIin": "2024-06-14", "PIout": "2024-07-11", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "17", "list_title": "6AABS202M00,E6.7 MX3 Bonding - 17 wafers", "list_modules": "Eng lot : FF66643 , MX3", "list_project": "EIGER"}, {"list_projects_names": "6AACT201M00,EIG RIB DEV, start : 2024-08-04 , 76 days", "list_Start_ON": "2024-08-04", "list_Due_ON": "2024-10-19", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "76", "RFQPOin": "2024-08-04", "RFQPOout": "2024-08-14", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-04", "TOout": "2024-08-29", "PIin": "2024-08-29", "PIout": "2024-10-14", "PrePin": "", "PrePout": "", "Testout": "2024-10-16", "DataProcessOut": "2024-10-17", "ShipmentOut": "2024-10-19", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "12", "list_title": "6AACT201M00,EIG RIB DEV", "list_modules": "Eng lot : FF66223 , MX3 , ILDA , MX3RIB", "list_project": "EIGER"}, {"list_projects_names": "5AACU101M00,MPW-AN800-34_2, start : 2024-08-12 , 143 days", "list_Start_ON": "2024-08-12", "list_Due_ON": "2025-01-02", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "143", "RFQPOin": "2024-08-12", "RFQPOout": "2024-08-22", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-12", "TOout": "2024-08-30", "PIin": "2024-08-30", "PIout": "2024-12-05", "PrePin": "", "PrePout": "", "Testout": "2024-12-06", "DataProcessOut": "2024-12-07", "ShipmentOut": "2024-12-09", "CSout": "", "BackendOut": "2025-01-02", "list_requester": "<PERSON>", "WaferNumberList": "9", "list_title": "5AACU101M00,MPW-AN800-34_2", "list_modules": "Proto lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA , CS", "list_project": "MPW"}, {"list_projects_names": "6AACV201M00,E2.12 SCTR1.c LOT1, start : 2024-07-18 , 61 days", "list_Start_ON": "2024-07-18", "list_Due_ON": "2024-09-17", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "61", "RFQPOin": "2024-07-18", "RFQPOout": "2024-07-28", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-18", "TOout": "2024-08-12", "PIin": "2024-08-12", "PIout": "2024-09-11", "PrePin": "", "PrePout": "", "Testout": "2024-09-14", "DataProcessOut": "2024-09-15", "ShipmentOut": "2024-09-17", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACV201M00,E2.12 SCTR1.c LOT1", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AACV202M00,E2.12 SCTR1.c LOT2, start : 2024-09-01 , 35 days", "list_Start_ON": "2024-09-01", "list_Due_ON": "2024-10-06", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "35", "RFQPOin": "2024-09-02", "RFQPOout": "2024-09-12", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-01", "PIout": "2024-09-30", "PrePin": "", "PrePout": "", "Testout": "2024-10-03", "DataProcessOut": "2024-10-04", "ShipmentOut": "2024-10-06", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACV202M00,E2.12 SCTR1.c LOT2", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AACV203M00,E2.12 SCTR1.c LOT3, start : 2024-09-01 , 53 days", "list_Start_ON": "2024-09-01", "list_Due_ON": "2024-10-24", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "53", "RFQPOin": "2024-10-14", "RFQPOout": "2024-10-24", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-01", "PIout": "2024-09-30", "PrePin": "", "PrePout": "", "Testout": "2024-10-03", "DataProcessOut": "2024-10-04", "ShipmentOut": "2024-10-06", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACV203M00,E2.12 SCTR1.c LOT3", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AACW201M00,ETV6p2, start : 2024-08-22 , 71 days", "list_Start_ON": "2024-08-22", "list_Due_ON": "2024-11-01", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "71", "RFQPOin": "2024-08-22", "RFQPOout": "2024-09-01", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-22", "TOout": "2024-09-16", "PIin": "2024-09-16", "PIout": "2024-10-25", "PrePin": "", "PrePout": "", "Testout": "2024-10-28", "DataProcessOut": "2024-10-29", "ShipmentOut": "2024-11-01", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACW201M00,ETV6p2", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AACX101M00,E2.15 Wolverine3 TRIM., start : 2025-01-10 , 98 days", "list_Start_ON": "2025-01-10", "list_Due_ON": "2025-04-18", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "98", "RFQPOin": "2025-01-10", "RFQPOout": "2025-01-20", "ERFPOin": "", "ERFPOout": "", "TOin": "2025-01-10", "TOout": "2025-02-04", "PIin": "2025-02-04", "PIout": "2025-04-11", "PrePin": "", "PrePout": "", "Testout": "2025-04-14", "DataProcessOut": "2025-04-15", "ShipmentOut": "2025-04-18", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACX101M00,E2.15 Wolverine3 TRIM.", "list_modules": "Proto lot : FF66223-1mm , MX3 , ILDA , HEATER , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "6AACY101M00,EIGER LSP4, start : 2024-11-08 , 96 days", "list_Start_ON": "2024-11-08", "list_Due_ON": "2025-02-12", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "96", "RFQPOin": "2024-11-08", "RFQPOout": "2024-11-18", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-11-08", "TOout": "2024-12-03", "PIin": "2024-12-03", "PIout": "2025-02-07", "PrePin": "", "PrePout": "", "Testout": "2025-02-08", "DataProcessOut": "2025-02-09", "ShipmentOut": "2025-02-12", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "6", "list_title": "6AACY101M00,EIGER LSP4", "list_modules": "Proto lot : FF66223-1mm , MX3 , ILDA , HEATER , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AACZ101M00,E2.23 HAD2, start : 2024-11-22 , 118 days", "list_Start_ON": "2024-11-22", "list_Due_ON": "2025-03-20", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "118", "RFQPOin": "2024-11-22", "RFQPOout": "2024-12-02", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-11-22", "TOout": "2024-12-17", "PIin": "2024-12-17", "PIout": "2025-03-13", "PrePin": "", "PrePout": "", "Testout": "2025-03-16", "DataProcessOut": "2025-03-17", "ShipmentOut": "2025-03-20", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACZ101M00,E2.23 HAD2", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AACZ102M00,E2.23 HAD2, start : 2024-12-17 , 92 days", "list_Start_ON": "2024-12-17", "list_Due_ON": "2025-03-19", "list_corridor": "Prototype", "list_tech": "AN350", "list_cycleTime": "92", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-12-17", "PIout": "2025-03-13", "PrePin": "", "PrePout": "", "Testout": "2025-03-16", "DataProcessOut": "2025-03-17", "ShipmentOut": "2025-03-19", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AACZ102M00,E2.23 HAD2", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , MX3RIB , UCUT", "list_project": "EIGER"}, {"list_projects_names": "6AABL202M00,E2.12 SCTR1.b, start : 2024-07-18 , 55 days", "list_Start_ON": "2024-07-18", "list_Due_ON": "2024-09-11", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "55", "RFQPOin": "2024-07-21", "RFQPOout": "2024-07-31", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-07-18", "PIout": "2024-09-05", "PrePin": "", "PrePout": "", "Testout": "2024-09-08", "DataProcessOut": "2024-09-09", "ShipmentOut": "2024-09-11", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABL202M00,E2.12 SCTR1.b", "list_modules": "Eng lot : FF66223-1mm , MX3 , RESIST_OPEN", "list_project": "EIGER"}, {"list_projects_names": "5AADC201M00,MPW-LN-02, start : 2024-12-06 , 67 days", "list_Start_ON": "2024-12-06", "list_Due_ON": "2025-02-11", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "67", "RFQPOin": "2024-12-06", "RFQPOout": "2024-12-16", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-12-06", "TOout": "2024-12-24", "PIin": "2024-12-24", "PIout": "2025-02-06", "PrePin": "", "PrePout": "", "Testout": "2025-02-07", "DataProcessOut": "2025-02-08", "ShipmentOut": "2025-02-11", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON>", "WaferNumberList": "8", "list_title": "5AADC201M00,MPW-LN-02", "list_modules": "Eng lot : FF66223 , CO<PERSON> , X3", "list_project": "Dedicated run AN800"}, {"list_projects_names": "6AABS103M00,MERLIN - RERUN, start : 2024-09-05 , 34 days", "list_Start_ON": "2024-09-05", "list_Due_ON": "2024-10-09", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "34", "RFQPOin": "2024-08-25", "RFQPOout": "2024-09-04", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-05", "PIout": "2024-10-03", "PrePin": "", "PrePout": "", "Testout": "2024-10-06", "DataProcessOut": "2024-10-07", "ShipmentOut": "2024-10-09", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABS103M00,MERLIN - R<PERSON><PERSON>", "list_modules": "Proto lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABS104M00,MERLIN - RERUN2, start : 2024-11-12 , 34 days", "list_Start_ON": "2024-11-12", "list_Due_ON": "2024-12-16", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "34", "RFQPOin": "2024-11-01", "RFQPOout": "2024-11-11", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-11-12", "PIout": "2024-12-10", "PrePin": "", "PrePout": "", "Testout": "2024-12-13", "DataProcessOut": "2024-12-14", "ShipmentOut": "2024-12-16", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABS104M00,MERLIN - RERUN2", "list_modules": "Proto lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AABS105M00,MERLIN - RERUN3, start : 2024-11-21 , 34 days", "list_Start_ON": "2024-11-21", "list_Due_ON": "2024-12-25", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "34", "RFQPOin": "2024-11-10", "RFQPOout": "2024-11-20", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-11-21", "PIout": "2024-12-19", "PrePin": "", "PrePout": "", "Testout": "2024-12-22", "DataProcessOut": "2024-12-23", "ShipmentOut": "2024-12-25", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AABS105M00,MERLIN - RERUN3", "list_modules": "Proto lot : FF66643 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "6AADD201M00,2nd Metal Layer - Shortloop, start : 2024-09-09 , 93 days", "list_Start_ON": "2024-09-09", "list_Due_ON": "2024-12-11", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "93", "RFQPOin": "2024-09-09", "RFQPOout": "2024-09-19", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-09", "TOout": "2024-10-04", "PIin": "2024-10-04", "PIout": "2024-12-04", "PrePin": "", "PrePout": "", "Testout": "2024-12-07", "DataProcessOut": "2024-12-08", "ShipmentOut": "2024-12-11", "CSout": "", "BackendOut": "", "list_requester": "<PERSON>", "WaferNumberList": "25", "list_title": "6AADD201M00,2nd Metal Layer - Shortloop", "list_modules": "Eng lot : FF66223 , MX3 , ILDA", "list_project": "EIGER"}, {"list_projects_names": "4AACB201MX2,MAGNIFY-RUN 1,MX2 bonding, start : 2024-07-26 , 18 days", "list_Start_ON": "2024-07-26", "list_Due_ON": "2024-08-13", "list_corridor": "Engineering", "list_tech": "AN200", "list_cycleTime": "18", "RFQPOin": "2024-06-30", "RFQPOout": "2024-07-02", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-26", "TOout": "2024-08-13", "PIin": "2024-07-16", "PIout": "2024-08-05", "PrePin": "", "PrePout": "", "Testout": "", "DataProcessOut": "", "ShipmentOut": "2024-08-08", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "8", "list_title": "4AACB201MX2,MAGNIFY-RUN 1,MX2 bonding", "list_modules": "Eng lot : FF66223 , MX2", "list_project": "MAGNIFY"}, {"list_projects_names": "6AADE101M00,2235_29274, start : 2024-11-07 , 135 days", "list_Start_ON": "2024-11-07", "list_Due_ON": "2025-03-22", "list_corridor": "Engineering", "list_tech": "AN350", "list_cycleTime": "135", "RFQPOin": "2024-11-07", "RFQPOout": "2024-11-17", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-11-07", "TOout": "2024-12-02", "PIin": "2024-12-02", "PIout": "2025-02-21", "PrePin": "", "PrePout": "", "Testout": "2025-02-22", "DataProcessOut": "2025-02-23", "ShipmentOut": "2025-02-26", "CSout": "", "BackendOut": "2025-03-22", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "10", "list_title": "6AADE101M00,2235_29274", "list_modules": "Proto lot : FF66223 , MX3 , ILDA , HEATER , CSPLUS , UCUT", "list_project": "Dedicated run AN350"}, {"list_projects_names": "5AACH101ILA,0586_240725270,ILDA, start : 2024-08-30 , 145 days", "list_Start_ON": "2024-08-30", "list_Due_ON": "2025-01-22", "list_corridor": "Engineering", "list_tech": "AN800", "list_cycleTime": "145", "RFQPOin": "2024-08-30", "RFQPOout": "2024-09-09", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-08-30", "TOout": "2024-09-17", "PIin": "2024-09-17", "PIout": "2024-12-24", "PrePin": "", "PrePout": "", "Testout": "2024-12-25", "DataProcessOut": "2024-12-26", "ShipmentOut": "2024-12-29", "CSout": "", "BackendOut": "2025-01-22", "list_requester": "<PERSON>", "WaferNumberList": "3", "list_title": "5AACH101ILA,0586_240725270,ILDA", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA", "list_project": "Dedicated run AN800"}, {"list_projects_names": "5AACL201M17,PF_WP8_LNOI, start : 2024-10-03 , 59 days", "list_Start_ON": "2024-10-03", "list_Due_ON": "2024-12-01", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "59", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-10-03", "TOout": "2024-10-21", "PIin": "2024-10-21", "PIout": "2024-11-25", "PrePin": "", "PrePout": "", "Testout": "2024-11-28", "DataProcessOut": "2024-11-29", "ShipmentOut": "2024-12-01", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AACL201M17,PF_WP8_LNOI", "list_modules": "Eng lot : FF66223 , CORE", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "5AACQ201M00,PF_WP7/8_PD_02, start : 2024-09-02 , 121 days", "list_Start_ON": "2024-09-02", "list_Due_ON": "2025-01-01", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "121", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-09-02", "TOout": "2024-09-20", "PIin": "2024-09-20", "PIout": "2024-12-26", "PrePin": "", "PrePout": "", "Testout": "2024-12-29", "DataProcessOut": "2024-12-30", "ShipmentOut": "2025-01-01", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AACQ201M00,PF_WP7/8_PD_02", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "5AACQ202M00,PF_WP7/8_PD_02, start : 2024-09-20 , 103 days", "list_Start_ON": "2024-09-20", "list_Due_ON": "2025-01-01", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "103", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "", "TOout": "", "PIin": "2024-09-20", "PIout": "2024-12-26", "PrePin": "", "PrePout": "", "Testout": "2024-12-29", "DataProcessOut": "2024-12-30", "ShipmentOut": "2025-01-01", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AACQ202M00,PF_WP7/8_PD_02", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "6AACR201M00,PF_WP4_LOCA_S, start : 2024-10-15 , 130 days", "list_Start_ON": "2024-10-15", "list_Due_ON": "2025-02-22", "list_corridor": "PhotonixFAB", "list_tech": "AN350", "list_cycleTime": "130", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-10-15", "TOout": "2024-11-12", "PIin": "2024-11-12", "PIout": "2025-01-23", "PrePin": "", "PrePout": "", "Testout": "2025-01-26", "DataProcessOut": "2025-01-27", "ShipmentOut": "2025-01-29", "CSout": "", "BackendOut": "2025-02-22", "list_requester": "Houssein EL DIRANI", "WaferNumberList": "25", "list_title": "6AACR201M00,PF_WP4_LOCA_S", "list_modules": "Eng lot : FF66223 , MX3 , MX3X2 , LOCA-S , CS", "list_project": "PHOTONIXFAB"}, {"list_projects_names": "5AADA201M00,photonixFAB_WP8_01, start : 2024-07-29 , 121 days", "list_Start_ON": "2024-07-29", "list_Due_ON": "2024-11-27", "list_corridor": "PhotonixFAB", "list_tech": "AN800", "list_cycleTime": "121", "RFQPOin": "", "RFQPOout": "", "ERFPOin": "", "ERFPOout": "", "TOin": "2024-07-29", "TOout": "2024-08-16", "PIin": "2024-08-16", "PIout": "2024-11-21", "PrePin": "", "PrePout": "", "Testout": "2024-11-24", "DataProcessOut": "2024-11-25", "ShipmentOut": "2024-11-27", "CSout": "", "BackendOut": "", "list_requester": "<PERSON><PERSON><PERSON>", "WaferNumberList": "25", "list_title": "5AADA201M00,photonixFAB_WP8_01", "list_modules": "Eng lot : FF66223 , CORE , X2 , ILDA , HEATER , LOCA", "list_project": "PHOTONIXFAB"}]}