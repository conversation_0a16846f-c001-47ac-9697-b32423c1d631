.bootstrap-datetimepicker-widget .btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}

.bootstrap-datetimepicker-widget.dropdown-menu {
    position: absolute;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

.bootstrap-datetimepicker-widget .list-unstyled {
    padding-left: 0;
    list-style: none;
}

.bootstrap-datetimepicker-widget .collapse {
    display: none;
}

.bootstrap-datetimepicker-widget .collapse.in {
    display: block;
}

/* fix for bootstrap4 */
.bootstrap-datetimepicker-widget .table-condensed > thead > tr > th,
.bootstrap-datetimepicker-widget .table-condensed > tbody > tr > td,
.bootstrap-datetimepicker-widget .table-condensed > tfoot > tr > td {
    padding: 5px;
}
.input-group.dbdp~.valid-feedback,
.input-group.dbdp~.invalid-feedback {
    display: block;
}
.input-group.dbdp>.input-group-addon {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
