#!/bin/bash

# TALOS Auto-Start Script
# This script automatically starts TALOS Docker containers after system reboot

set -e

# Configuration - Auto-detect project directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_DIR/logs"
LOG_FILE="$LOG_DIR/talos-autostart.log"
LOCK_FILE="/tmp/talos-autostart.lock"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_message "ERROR: Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to wait for Docker to be ready
wait_for_docker() {
    log_message "Waiting for Docker to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker info >/dev/null 2>&1; then
            log_message "Docker is ready!"
            return 0
        fi
        log_message "Attempt $attempt/$max_attempts: Docker not ready yet, waiting 10 seconds..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    log_message "ERROR: Docker failed to start within 5 minutes"
    exit 1
}

# Function to start TALOS containers
start_talos() {
    log_message "Starting TALOS containers..."
    
    cd "$PROJECT_DIR" || {
        log_message "ERROR: Cannot change to project directory: $PROJECT_DIR"
        exit 1
    }
    
    # Pull latest images (optional)
    log_message "Pulling latest images..."
    docker-compose pull || log_message "WARNING: Failed to pull images, continuing with local images"
    
    # Start containers
    log_message "Starting containers with docker-compose..."
    if docker-compose up -d; then
        log_message "TALOS containers started successfully!"
    else
        log_message "ERROR: Failed to start TALOS containers"
        exit 1
    fi
    
    # Wait for services to be healthy
    log_message "Waiting for services to be healthy..."
    sleep 30
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        log_message "Services are running. Checking health..."
        
        # Test if TALOS is accessible
        local max_health_checks=12
        local health_check=1

        while [ $health_check -le $max_health_checks ]; do
            if curl -f http://localhost:8000/scheduling/ >/dev/null 2>&1; then
                log_message "SUCCESS: TALOS is accessible at http://localhost:8000/scheduling/"
                return 0
            fi
            log_message "Health check $health_check/$max_health_checks: TALOS not ready yet, waiting 10 seconds..."
            sleep 10
            health_check=$((health_check + 1))
        done
        
        log_message "WARNING: TALOS containers are running but application may not be fully ready"
    else
        log_message "ERROR: Some services failed to start properly"
        docker-compose ps
        exit 1
    fi
}

# Function to send notification (optional)
send_notification() {
    local status="$1"
    local message="$2"
    
    # You can customize this to send notifications via email, Slack, etc.
    log_message "NOTIFICATION [$status]: $message"
    
    # Example: Send email notification (uncomment and configure if needed)
    # echo "$message" | mail -s "TALOS Auto-Start $status" <EMAIL>
}

# Main execution
main() {
    # Check if already running (prevent multiple instances)
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "Auto-start script is already running (PID: $pid)"
            exit 0
        else
            log_message "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    
    # Create lock file
    echo $$ > "$LOCK_FILE"
    
    # Ensure lock file is removed on exit
    trap 'rm -f "$LOCK_FILE"' EXIT
    
    log_message "=== TALOS Auto-Start Script Started ==="
    log_message "Project directory: $PROJECT_DIR"
    log_message "System uptime: $(uptime)"
    
    # Wait for Docker to be ready
    wait_for_docker
    
    # Start TALOS
    if start_talos; then
        send_notification "SUCCESS" "TALOS containers started successfully after system reboot"
        log_message "=== TALOS Auto-Start Completed Successfully ==="
    else
        send_notification "FAILED" "TALOS auto-start failed after system reboot"
        log_message "=== TALOS Auto-Start Failed ==="
        exit 1
    fi
}

# Run main function
main "$@"
