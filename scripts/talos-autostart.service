[Unit]
Description=TALOS Docker Auto-Start Service
Documentation=https://docs.docker.com/
Wants=docker.service
After=docker.service network-online.target
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/opt/talos/scripts/auto-start-talos.sh
ExecStop=/usr/bin/docker-compose -f /opt/talos/docker-compose.yml down
WorkingDirectory=/opt/talos
User=talos
Group=talos

# Restart policy
Restart=on-failure
RestartSec=30

# Environment
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=DOCKER_HOST=unix:///var/run/docker.sock

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=talos-autostart

# Security
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
