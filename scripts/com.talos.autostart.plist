<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.talos.autostart</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>/Users/<USER>/my-talos-project/scripts/auto-start-talos.sh</string>
    </array>
    
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/my-talos-project</string>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <false/>
    
    <key>LaunchOnlyOnce</key>
    <true/>
    
    <key>StandardOutPath</key>
    <string>/Users/<USER>/my-talos-project/logs/talos-autostart.log</string>
    
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/my-talos-project/logs/talos-autostart-error.log</string>
    
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/opt/anaconda3/bin</string>
        <key>HOME</key>
        <string>/Users/<USER>/string>
    </dict>
    
    <key>StartInterval</key>
    <integer>300</integer>
    
    <key>ThrottleInterval</key>
    <integer>60</integer>
</dict>
</plist>
