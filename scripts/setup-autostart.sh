#!/bin/bash

# Universal TALOS Auto-Start Setup Script
# This script detects the operating system and sets up appropriate auto-start mechanism

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect operating system
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        echo "windows"
    else
        echo "unknown"
    fi
}

# Function to get project directory
get_project_dir() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    echo "$(dirname "$script_dir")"
}

# Function to setup macOS LaunchAgent
setup_macos() {
    local project_dir="$1"
    local plist_file="$HOME/Library/LaunchAgents/com.talos.autostart.plist"
    
    print_status "Setting up macOS LaunchAgent..."
    
    # Create logs directory
    mkdir -p "$project_dir/logs"
    
    # Create the plist file
    cat > "$plist_file" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.talos.autostart</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>$project_dir/scripts/auto-start-talos.sh</string>
    </array>
    
    <key>WorkingDirectory</key>
    <string>$project_dir</string>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <false/>
    
    <key>StandardOutPath</key>
    <string>$project_dir/logs/talos-autostart.log</string>
    
    <key>StandardErrorPath</key>
    <string>$project_dir/logs/talos-autostart-error.log</string>
    
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/opt/anaconda3/bin</string>
        <key>HOME</key>
        <string>$HOME</string>
    </dict>
</dict>
</plist>
EOF
    
    # Load the LaunchAgent
    launchctl unload "$plist_file" 2>/dev/null || true
    launchctl load "$plist_file"
    
    print_success "macOS LaunchAgent configured successfully"
    print_status "TALOS will auto-start on system boot"
    print_status "To manually start: launchctl start com.talos.autostart"
    print_status "To stop: launchctl stop com.talos.autostart"
    print_status "To disable: launchctl unload $plist_file"
}

# Function to setup Linux systemd service
setup_linux() {
    local project_dir="$1"
    local service_file="/etc/systemd/system/talos-autostart.service"
    
    print_status "Setting up Linux systemd service..."
    
    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]]; then
        print_error "This script needs to be run with sudo for Linux setup"
        print_status "Please run: sudo $0"
        exit 1
    fi
    
    # Create logs directory
    mkdir -p "$project_dir/logs"
    
    # Create the service file
    cat > "$service_file" << EOF
[Unit]
Description=TALOS Docker Auto-Start Service
Documentation=https://docs.docker.com/
Wants=docker.service
After=docker.service network-online.target
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=$project_dir/scripts/auto-start-talos.sh
ExecStop=/usr/bin/docker-compose -f $project_dir/docker-compose.yml down
WorkingDirectory=$project_dir
User=$SUDO_USER
Group=$SUDO_USER

# Restart policy
Restart=on-failure
RestartSec=30

# Environment
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=DOCKER_HOST=unix:///var/run/docker.sock

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=talos-autostart

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable talos-autostart.service
    
    print_success "Linux systemd service configured successfully"
    print_status "TALOS will auto-start on system boot"
    print_status "To manually start: sudo systemctl start talos-autostart"
    print_status "To stop: sudo systemctl stop talos-autostart"
    print_status "To disable: sudo systemctl disable talos-autostart"
    print_status "To check status: sudo systemctl status talos-autostart"
}

# Function to setup Windows (instructions only)
setup_windows() {
    local project_dir="$1"
    
    print_status "Windows auto-start setup instructions:"
    print_status "1. Copy the PowerShell script to your project directory"
    print_status "2. Open Task Scheduler as Administrator"
    print_status "3. Create a new task with these settings:"
    print_status "   - Name: TALOS Auto-Start"
    print_status "   - Trigger: At startup"
    print_status "   - Action: Start a program"
    print_status "   - Program: powershell.exe"
    print_status "   - Arguments: -ExecutionPolicy Bypass -File \"$project_dir\\scripts\\auto-start-talos.ps1\""
    print_status "   - Start in: $project_dir"
    print_status "4. Set 'Run with highest privileges'"
    print_status "5. Set 'Run whether user is logged on or not'"
    
    print_warning "Alternatively, you can use the batch file with Windows Startup folder"
}

# Main execution
main() {
    local project_dir
    local os_type
    
    print_status "TALOS Auto-Start Setup Script"
    print_status "=============================="
    
    # Get project directory
    project_dir=$(get_project_dir)
    print_status "Project directory: $project_dir"
    
    # Detect operating system
    os_type=$(detect_os)
    print_status "Detected OS: $os_type"
    
    # Make auto-start script executable
    chmod +x "$project_dir/scripts/auto-start-talos.sh"
    
    # Setup based on OS
    case $os_type in
        "macos")
            setup_macos "$project_dir"
            ;;
        "linux")
            setup_linux "$project_dir"
            ;;
        "windows")
            setup_windows "$project_dir"
            ;;
        *)
            print_error "Unsupported operating system: $os_type"
            print_status "Please manually configure auto-start for your system"
            exit 1
            ;;
    esac
    
    print_success "Auto-start setup completed!"
    print_status "TALOS will automatically start after system reboot"
    print_status "Logs will be available in: $project_dir/logs/"
}

# Run main function
main "$@"
