@echo off
REM TALOS Auto-Start Script for Windows
REM This script automatically starts TALOS Docker containers after system reboot

setlocal enabledelayedexpansion

REM Configuration
set "PROJECT_DIR=C:\talos-project"
set "LOG_FILE=%PROJECT_DIR%\logs\talos-autostart.log"
set "LOCK_FILE=%TEMP%\talos-autostart.lock"

REM Create logs directory if it doesn't exist
if not exist "%PROJECT_DIR%\logs" mkdir "%PROJECT_DIR%\logs"

REM Function to log messages
call :log_message "=== TALOS Auto-Start Script Started ==="

REM Check if already running
if exist "%LOCK_FILE%" (
    call :log_message "Auto-start script is already running"
    exit /b 0
)

REM Create lock file
echo %date% %time% > "%LOCK_FILE%"

REM Wait for Docker Desktop to start
call :log_message "Waiting for Docker Desktop to start..."
call :wait_for_docker

REM Change to project directory
cd /d "%PROJECT_DIR%" || (
    call :log_message "ERROR: Cannot change to project directory: %PROJECT_DIR%"
    goto :cleanup
)

REM Start TALOS containers
call :log_message "Starting TALOS containers..."
docker-compose up -d
if !errorlevel! neq 0 (
    call :log_message "ERROR: Failed to start TALOS containers"
    goto :cleanup
)

REM Wait for services to be ready
call :log_message "Waiting for services to be ready..."
timeout /t 30 /nobreak >nul

REM Check if TALOS is accessible
call :log_message "Checking TALOS accessibility..."
call :check_talos_health

call :log_message "=== TALOS Auto-Start Completed ==="
goto :cleanup

:log_message
echo %date% %time% - %~1 >> "%LOG_FILE%"
echo %date% %time% - %~1
exit /b

:wait_for_docker
set /a attempts=0
set /a max_attempts=30

:docker_loop
docker info >nul 2>&1
if !errorlevel! equ 0 (
    call :log_message "Docker is ready!"
    exit /b 0
)

set /a attempts+=1
if !attempts! geq !max_attempts! (
    call :log_message "ERROR: Docker failed to start within 5 minutes"
    exit /b 1
)

call :log_message "Attempt !attempts!/!max_attempts!: Docker not ready, waiting 10 seconds..."
timeout /t 10 /nobreak >nul
goto :docker_loop

:check_talos_health
set /a health_attempts=0
set /a max_health_attempts=12

:health_loop
curl -f http://localhost:8000/scheduling/ >nul 2>&1
if !errorlevel! equ 0 (
    call :log_message "SUCCESS: TALOS is accessible at http://localhost:8000/scheduling/"
    exit /b 0
)

set /a health_attempts+=1
if !health_attempts! geq !max_health_attempts! (
    call :log_message "WARNING: TALOS containers running but application may not be ready"
    exit /b 1
)

call :log_message "Health check !health_attempts!/!max_health_attempts!: waiting 10 seconds..."
timeout /t 10 /nobreak >nul
goto :health_loop

:cleanup
if exist "%LOCK_FILE%" del "%LOCK_FILE%"
exit /b
