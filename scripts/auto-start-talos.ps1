# TALOS Auto-Start Script for Windows PowerShell
# This script automatically starts TALOS Docker containers after system reboot

param(
    [string]$ProjectDir = "C:\talos-project",
    [int]$MaxDockerWait = 300,
    [int]$MaxHealthWait = 120
)

# Configuration
$LogFile = Join-Path $ProjectDir "logs\talos-autostart.log"
$LockFile = Join-Path $env:TEMP "talos-autostart.lock"

# Ensure logs directory exists
$LogDir = Split-Path $LogFile -Parent
if (!(Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# Function to log messages
function Write-Log {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "$Timestamp - $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry
}

# Function to check if Dock<PERSON> is running
function Test-DockerRunning {
    try {
        docker info 2>$null | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to wait for Docker
function Wait-ForDocker {
    Write-Log "Waiting for Docker to be ready..."
    $StartTime = Get-Date
    
    while ((Get-Date) - $StartTime -lt [TimeSpan]::FromSeconds($MaxDockerWait)) {
        if (Test-DockerRunning) {
            Write-Log "Docker is ready!"
            return $true
        }
        Write-Log "Docker not ready yet, waiting 10 seconds..."
        Start-Sleep -Seconds 10
    }
    
    Write-Log "ERROR: Docker failed to start within $MaxDockerWait seconds"
    return $false
}

# Function to test TALOS health
function Test-TalosHealth {
    try {
        $Response = Invoke-WebRequest -Uri "http://localhost:8000/scheduling/" -TimeoutSec 5 -UseBasicParsing
        return $Response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# Function to wait for TALOS to be healthy
function Wait-ForTalosHealth {
    Write-Log "Checking TALOS health..."
    $StartTime = Get-Date
    
    while ((Get-Date) - $StartTime -lt [TimeSpan]::FromSeconds($MaxHealthWait)) {
        if (Test-TalosHealth) {
            Write-Log "SUCCESS: TALOS is accessible at http://localhost:8000/scheduling/"
            return $true
        }
        Write-Log "TALOS not ready yet, waiting 10 seconds..."
        Start-Sleep -Seconds 10
    }
    
    Write-Log "WARNING: TALOS containers running but application may not be ready"
    return $false
}

# Main execution
try {
    # Check if already running
    if (Test-Path $LockFile) {
        $LockContent = Get-Content $LockFile -ErrorAction SilentlyContinue
        if ($LockContent) {
            $ProcessId = [int]$LockContent
            if (Get-Process -Id $ProcessId -ErrorAction SilentlyContinue) {
                Write-Log "Auto-start script is already running (PID: $ProcessId)"
                exit 0
            }
        }
        Remove-Item $LockFile -Force -ErrorAction SilentlyContinue
    }
    
    # Create lock file
    $PID | Out-File -FilePath $LockFile -Encoding ASCII
    
    Write-Log "=== TALOS Auto-Start Script Started ==="
    Write-Log "Project directory: $ProjectDir"
    Write-Log "System uptime: $((Get-CimInstance Win32_OperatingSystem).LastBootUpTime)"
    
    # Wait for Docker
    if (!(Wait-ForDocker)) {
        throw "Docker failed to start"
    }
    
    # Change to project directory
    if (!(Test-Path $ProjectDir)) {
        throw "Project directory does not exist: $ProjectDir"
    }
    
    Set-Location $ProjectDir
    
    # Start TALOS containers
    Write-Log "Starting TALOS containers..."
    $Result = & docker-compose up -d 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Log "ERROR: Failed to start TALOS containers"
        Write-Log "Docker-compose output: $Result"
        throw "Failed to start containers"
    }
    
    Write-Log "Containers started, waiting for services to be ready..."
    Start-Sleep -Seconds 30
    
    # Check health
    Wait-ForTalosHealth | Out-Null
    
    Write-Log "=== TALOS Auto-Start Completed Successfully ==="
    
    # Send notification (optional)
    # You can add email notification here if needed
    
}
catch {
    Write-Log "ERROR: $($_.Exception.Message)"
    Write-Log "=== TALOS Auto-Start Failed ==="
    exit 1
}
finally {
    # Cleanup lock file
    if (Test-Path $LockFile) {
        Remove-Item $LockFile -Force -ErrorAction SilentlyContinue
    }
}
