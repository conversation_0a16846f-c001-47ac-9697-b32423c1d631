import os
from .settings import *

# Override settings for Docker deployment

# Database configuration for PostgreSQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_DB', 'talos_db'),
        'USER': os.environ.get('POSTGRES_USER', 'talos_user'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD', 'talos_password'),
        'HOST': os.environ.get('DB_HOST', 'talos-db'),
        'PORT': os.environ.get('DB_PORT', '5432'),
    }
}

# Security settings for production
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1,0.0.0.0').split(',')

# Static files configuration
STATIC_ROOT = '/app/staticfiles'
STATIC_URL = '/static/'

# Media files configuration
MEDIA_ROOT = '/app/media'
MEDIA_URL = '/media/'

# Cache configuration with Redis
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://talos-redis:6379/1'),
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': os.environ.get('DJANGO_LOG_LEVEL', 'INFO'),
            'propagate': False,
        },
    },
}

# Security headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Time zone
USE_TZ = True
TIME_ZONE = 'Europe/Paris'
