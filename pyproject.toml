[build-system]
requires = [
  "setuptools>=61.0",
  "setuptools-scm>=8.0"
]
build-backend = "setuptools.build_meta"

[project]
name = "capacity_management"
dynamic = ["version"] # DO NOT CHANGE
authors = [
  { name="Fingal Persoud", email="<EMAIL>" },
  { name="<PERSON><PERSON><PERSON>", email="<EMAIL>" },
]
description = "Management of the lot capacity in the 200mm line"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
dependencies = []

[project.optional-dependencies]
dev = []


[project.urls]
"Homepage" = "http://gitlab.lig.local/operations/talos"
