# TALOS Auto-Start Setup Guide

This guide explains how to set up automatic startup of TALOS Docker containers after system reboot across different operating systems.

## 🚀 Quick Setup (All OS)

Run the universal setup script:

```bash
chmod +x scripts/setup-autostart.sh
./scripts/setup-autostart.sh
```

This script will automatically detect your OS and configure the appropriate auto-start mechanism.

## 📋 Manual Setup by Operating System

### 🍎 macOS Setup

1. **Make scripts executable:**
   ```bash
   chmod +x scripts/auto-start-talos.sh
   chmod +x scripts/setup-autostart.sh
   ```

2. **Install LaunchAgent:**
   ```bash
   cp scripts/com.talos.autostart.plist ~/Library/LaunchAgents/
   launchctl load ~/Library/LaunchAgents/com.talos.autostart.plist
   ```

3. **Manual commands:**
   ```bash
   # Start manually
   launchctl start com.talos.autostart
   
   # Stop
   launchctl stop com.talos.autostart
   
   # Disable auto-start
   launchctl unload ~/Library/LaunchAgents/com.talos.autostart.plist
   ```

### 🐧 Linux Setup (Ubuntu/CentOS/RHEL)

1. **Make scripts executable:**
   ```bash
   chmod +x scripts/auto-start-talos.sh
   ```

2. **Install systemd service (requires sudo):**
   ```bash
   sudo cp scripts/talos-autostart.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable talos-autostart.service
   ```

3. **Manual commands:**
   ```bash
   # Start manually
   sudo systemctl start talos-autostart
   
   # Stop
   sudo systemctl stop talos-autostart
   
   # Check status
   sudo systemctl status talos-autostart
   
   # View logs
   sudo journalctl -u talos-autostart -f
   
   # Disable auto-start
   sudo systemctl disable talos-autostart
   ```

### 🪟 Windows Setup

#### Option 1: Task Scheduler (Recommended)

1. **Open Task Scheduler as Administrator**

2. **Create Basic Task:**
   - Name: `TALOS Auto-Start`
   - Description: `Automatically start TALOS Docker containers`

3. **Trigger:**
   - When: `When the computer starts`

4. **Action:**
   - Action: `Start a program`
   - Program: `powershell.exe`
   - Arguments: `-ExecutionPolicy Bypass -File "C:\path\to\talos\scripts\auto-start-talos.ps1"`
   - Start in: `C:\path\to\talos`

5. **Settings:**
   - ✅ Run with highest privileges
   - ✅ Run whether user is logged on or not
   - ✅ Wake the computer to run this task

#### Option 2: Startup Folder

1. **Copy batch file to startup folder:**
   ```cmd
   copy scripts\auto-start-talos.bat "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\"
   ```

2. **Edit the batch file to update PROJECT_DIR path**

## 🔧 Configuration

### Environment Variables

You can customize the auto-start behavior by setting these environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `TALOS_PROJECT_DIR` | Auto-detected | Project directory path |
| `TALOS_MAX_DOCKER_WAIT` | `300` | Max seconds to wait for Docker |
| `TALOS_MAX_HEALTH_WAIT` | `120` | Max seconds to wait for health check |
| `TALOS_LOG_LEVEL` | `INFO` | Logging level |

### Custom Project Directory

If your project is not in the default location, update the paths in:

- **macOS**: `~/Library/LaunchAgents/com.talos.autostart.plist`
- **Linux**: `/etc/systemd/system/talos-autostart.service`
- **Windows**: Task Scheduler task or startup batch file

## 📊 Monitoring and Logs

### Log Locations

- **All OS**: `{project_dir}/logs/talos-autostart.log`
- **Linux systemd**: `sudo journalctl -u talos-autostart`
- **macOS**: Console.app → search for "talos"
- **Windows**: Event Viewer → Windows Logs → Application

### Health Check

The auto-start script includes health checks:

1. **Docker availability** - Waits up to 5 minutes for Docker to start
2. **Container startup** - Verifies containers are running
3. **Application health** - Tests HTTP endpoint accessibility
4. **Logging** - Comprehensive logging of all steps

## 🛠️ Troubleshooting

### Common Issues

1. **Docker not starting:**
   ```bash
   # Check Docker status
   docker info
   
   # Restart Docker service (Linux)
   sudo systemctl restart docker
   
   # Restart Docker Desktop (macOS/Windows)
   ```

2. **Permission issues (Linux):**
   ```bash
   # Add user to docker group
   sudo usermod -aG docker $USER
   
   # Restart session or reboot
   ```

3. **Script not executable:**
   ```bash
   chmod +x scripts/auto-start-talos.sh
   ```

4. **Path issues:**
   - Verify project directory path in configuration files
   - Check that docker-compose.yml exists in project root

### Manual Testing

Test the auto-start script manually:

```bash
# Run the script directly
./scripts/auto-start-talos.sh

# Check logs
tail -f logs/talos-autostart.log

# Verify containers are running
docker-compose ps

# Test application
curl http://localhost:8000/scheduling/
```

## 🔄 Updating

When you update the TALOS project:

1. **Pull latest changes**
2. **Rebuild containers:**
   ```bash
   docker-compose down
   docker-compose up --build -d
   ```
3. **No need to reconfigure auto-start** (unless paths change)

## 🗑️ Uninstalling Auto-Start

### macOS
```bash
launchctl unload ~/Library/LaunchAgents/com.talos.autostart.plist
rm ~/Library/LaunchAgents/com.talos.autostart.plist
```

### Linux
```bash
sudo systemctl disable talos-autostart
sudo systemctl stop talos-autostart
sudo rm /etc/systemd/system/talos-autostart.service
sudo systemctl daemon-reload
```

### Windows
- Delete the task from Task Scheduler, or
- Remove the batch file from the Startup folder

## 📞 Support

If you encounter issues:

1. Check the logs in `logs/talos-autostart.log`
2. Verify Docker is running: `docker info`
3. Test manual startup: `docker-compose up -d`
4. Check container status: `docker-compose ps`
