# Changelog

All notable changes to this project will be documented in this file. See
[Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.2.6](http://gitlab.lig.local/operations/talos/compare/v1.2.5...v1.2.6) (2024-11-04)

### Bug Fixes

* add alaa as responsible for QA gate AN800 ([7e19a4a](http://gitlab.lig.local/operations/talos/commit/7e19a4a8ddeae4e25d47688c05d8120fba6c4b1f))
* add guidelines and QA gates ([6fbcac5](http://gitlab.lig.local/operations/talos/commit/6fbcac5320fc46a42bed5dcebe3d9c8fcc17dca0))

## [1.2.5](http://gitlab.lig.local/operations/talos/compare/v1.2.4...v1.2.5) (2024-06-10)

### Bug Fixes

* add photonixFAB corridor ([d49ae92](http://gitlab.lig.local/operations/talos/commit/d49ae92b49f4a7576dc9602b7953a1a3705ddb80))

## [1.2.4](http://gitlab.lig.local/operations/talos/compare/v1.2.3...v1.2.4) (2024-05-28)


### Bug Fixes

* more info on the lot ([0c1cfff](http://gitlab.lig.local/operations/talos/commit/0c1cfffbc6b6da1d47203ec41b0698d252f56799))

## [1.2.3](http://gitlab.lig.local/operations/talos/compare/v1.2.2...v1.2.3) (2024-04-24)


### Bug Fixes

* add Timeline Per Task ([370096d](http://gitlab.lig.local/operations/talos/commit/370096dd9f67639a651f9987e0a5511274cb819e))

## [1.2.2](http://gitlab.lig.local/operations/talos/compare/v1.2.1...v1.2.2) (2024-04-04)


### Bug Fixes

* add version in pyproject.toml ([c2d543c](http://gitlab.lig.local/operations/talos/commit/c2d543c06f322224822fb6369ed7dd74ad8f722d))
* Revert version in pyproject.toml ([0b083f9](http://gitlab.lig.local/operations/talos/commit/0b083f98c4a073be79137ef2b92ffc4b22ae212f))
* update all modif ([4fc5b5a](http://gitlab.lig.local/operations/talos/commit/4fc5b5af8d50d2def31f5f80bcbdc19e4c5b904a))
* update link talos project ([6b9da3b](http://gitlab.lig.local/operations/talos/commit/6b9da3b11f27823e0a6e3c4b97d274d008454bbd))

## [1.2.1](http://gitlab.lig.local/operations/talos/compare/v1.2.0...v1.2.1) (2024-03-25)


### Bug Fixes

* add please wait ([b617dd6](http://gitlab.lig.local/operations/talos/commit/b617dd66fa161f7d4fa0598906162c019e633b27))

## [1.2.0](http://gitlab.lig.local/operations/talos/compare/v1.1.1...v1.2.0) (2024-03-22)


### Features

* change to django ([28493da](http://gitlab.lig.local/operations/talos/commit/28493da5811b42bb6d6e4a5f3e7735d043a8cc63))


### Bug Fixes

* add .gitkeep in static ([d39dff6](http://gitlab.lig.local/operations/talos/commit/d39dff616b466592d0bb39e5581cb2419a244880))

## [1.1.1](http://gitlab.lig.local/operations/talos/compare/v1.1.0...v1.1.1) (2024-03-12)


### Bug Fixes

* update_readme ([1a182df](http://gitlab.lig.local/operations/talos/commit/1a182df448427ef669cf08cc16fb6f5284df59d1))

## [1.1.0](http://gitlab.lig.local/operations/talos/compare/v1.0.1...v1.1.0) (2024-03-12)


### Features

* Edit README ([36858c4](http://gitlab.lig.local/operations/talos/commit/36858c4232a64541a5bee0c203a088565fa9ec78))

## [1.0.1](http://gitlab.lig.local/operations/talos/compare/v1.0.0...v1.0.1) (2024-03-12)


### Bug Fixes

* Rename to Talos ([0edea98](http://gitlab.lig.local/operations/talos/commit/0edea984c6e730f860658cd5ddc82bedd4bfc2db))

## 1.0.0 (2024-1-26)


### Bug Fixes

* new repository initialise ([3f121ad](http://gitlab.lig.local/operations/capacity-management/commit/3f121ad1b34c4f61d9505e80cb9fb8c555215c7a))
