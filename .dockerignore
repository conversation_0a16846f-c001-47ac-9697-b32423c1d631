# dockerignore
# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
talos-env/
venv/
ENV/
env/
.venv/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Environment files
.env
.env.local
.env.production

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
docs/

# Temporary files
get-pip.py
*.tmp
*.temp

# Node modules (if any)
node_modules/
npm-debug.log*

# Build artifacts
build/
dist/
*.egg-info/
