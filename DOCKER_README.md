# TALOS Docker Deployment

This guide explains how to run the TALOS semiconductor fabrication capacity management system using Docker containers.

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+

## 🚀 Quick Start

1. **Clone and navigate to project:**
   ```bash
   git clone <repository-url>
   cd talos-project
   ```

2. **Build and start the containers:**
   ```bash
   docker-compose up --build -d
   ```

3. **Access the application:**
   - **TALOS Application**: http://localhost:8000/scheduling/
   - **Django Admin**: http://localhost:8000/admin/

4. **Default admin credentials:**
   - Username: `admin`
   - Password: `admin123`

## Services

The Docker setup includes:

- **talos-web**: Main Django application
- **talos-db**: PostgreSQL database
- **talos-redis**: Redis cache server

## Environment Variables

You can customize the deployment by setting these environment variables in `docker-compose.yml`:

| Variable | Default | Description |
|----------|---------|-------------|
| `DEBUG` | `False` | Django debug mode |
| `POSTGRES_DB` | `talos_db` | Database name |
| `POSTGRES_USER` | `talos_user` | Database user |
| `POSTGRES_PASSWORD` | `talos_password` | Database password |
| `ALLOWED_HOSTS` | `localhost,127.0.0.1,0.0.0.0` | Allowed hosts |

## Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f talos-web
```

### Run Django commands
```bash
docker-compose exec talos-web python manage.py <command>
```

### Database backup
```bash
docker-compose exec talos-db pg_dump -U talos_user talos_db > backup.sql
```

### Database restore
```bash
docker-compose exec -T talos-db psql -U talos_user talos_db < backup.sql
```

## Data Persistence

The following data is persisted in Docker volumes:

- **postgres_data**: Database files
- **redis_data**: Redis cache data
- **talos_static**: Static files
- **./talos/scheduling/temp**: Application temp files (host mounted)
- **./media**: Media files (host mounted)

## Production Deployment

For production deployment:

1. **Change default passwords** in `docker-compose.yml`
2. **Set up SSL/TLS** with a reverse proxy (nginx/traefik)
3. **Configure backup strategy** for database
4. **Set appropriate resource limits**
5. **Use Docker secrets** for sensitive data

## Troubleshooting

### Container won't start
```bash
docker-compose logs talos-web
```

### Database connection issues
```bash
docker-compose exec talos-web nc -z talos-db 5432
```

### Reset everything
```bash
docker-compose down -v
docker-compose up --build -d
```

## Development

For development with live code reloading:

```bash
# Override the command to enable debug mode
docker-compose run --rm -p 8000:8000 -e DEBUG=True talos-web python manage.py runserver 0.0.0.0:8000
```
