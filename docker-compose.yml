services:
  talos-web:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: talos-app
    ports:
      - "${TALOS_PORT:-8000}:8000"
    volumes:
      - ./scheduling/temp:/app/scheduling/temp
      - ./media:/app/media
      - talos_static:/app/staticfiles
      - talos_logs:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=Talos.docker_settings
      - PYTHONPATH=/app
      - DEBUG=${DEBUG:-False}
      - SECRET_KEY=${SECRET_KEY}
      - POSTGRES_DB=${POSTGRES_DB:-talos_db}
      - POSTGRES_USER=${POSTGRES_USER:-talos_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=talos-db
      - DB_PORT=5432
      - REDIS_URL=redis://talos-redis:6379/1
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1,talos-web}
    depends_on:
      talos-db:
        condition: service_healthy
      talos-redis:
        condition: service_started
    restart: unless-stopped
    networks:
      - talos-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/scheduling/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  talos-db:
    image: postgres:15-alpine
    container_name: talos-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-talos_db}
      POSTGRES_USER: ${POSTGRES_USER:-talos_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    restart: unless-stopped
    networks:
      - talos-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-talos_user} -d ${POSTGRES_DB:-talos_db}"]
      interval: 10s
      timeout: 5s
      retries: 5

  talos-redis:
    image: redis:7-alpine
    container_name: talos-redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - talos-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Optional: Nginx reverse proxy for production
  talos-nginx:
    image: nginx:alpine
    container_name: talos-nginx
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - talos_static:/var/www/static:ro
      - ./media:/var/www/media:ro
    depends_on:
      - talos-web
    restart: unless-stopped
    networks:
      - talos-network
    # Traefik labels for service discovery
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.talos.rule=Host(`talos.docker.lig.local`)"
      - "traefik.http.routers.talos.entrypoints=web"
      - "traefik.http.services.talos.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik_web"

volumes:
  postgres_data:
  redis_data:
  talos_static:
  talos_logs:

networks:
  talos-network:
    driver: bridge
  traefik_web:
    external: true