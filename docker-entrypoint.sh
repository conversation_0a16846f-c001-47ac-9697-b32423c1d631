# Docker entrypoint script for TALOS Django application
#!/bin/bash
set -e

# Wait for database to be ready
echo "Waiting for database..."
while ! nc -z talos-db 5432; do
  sleep 0.1
done
echo "Database is ready!"

# Stay in app directory (Django files are now at root)
cd /app

# Run database migrations
echo "Running database migrations..."
python manage.py migrate --noinput

# Create superuser if it doesn't exist
echo "Creating superuser if needed..."
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
EOF

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Start the application
echo "Starting TALOS application..."
exec "$@"
